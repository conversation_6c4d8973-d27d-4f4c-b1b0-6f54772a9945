from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Response, Request, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import io
import tempfile
import os
import psutil
import platform
import time
import asyncio
from datetime import datetime, timedelta
import MetaTrader5 as mt5

from app.config import config, Config
from app.mt5_manager import mt5_manager
from app.main import initialize_components
from datetime import datetime, date
from app.postgresql_memory import PostgresMemory
from app.explainability import ExplainabilityEngine
from app.vectorbt_service import vectorbt_service
from app.news_strategy import run_sentiment_strategy, fetch_news, score_headlines, make_decision
from app.voice_agent import VoiceAgent
from app.cost_optimizer import cost_optimizer
from app.settings_manager import settings_manager
from app.multi_account_manager import multi_account_manager
from app.professional_trading_sessions import professional_sessions


app = FastAPI(
    title="NexGen Forex Trading Bot API - Advanced AI Edition",
    description="🚀 Advanced AI Trading Bot with LLM Reasoning, Autonomous Learning & Human-AI Collaboration",
    version="2.0.0"
)

# Add CORS middleware to allow frontend requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

start_time = time.time()

@app.on_event("startup")
async def startup_event():
    """Initialize components on startup"""
    try:
        print("Initializing bot components...")
        components = initialize_components()

        # Initialize shared state
        if not hasattr(app.state, 'shared_state'):
            app.state.shared_state = {}

        app.state.shared_state["components"] = components
        app.state.shared_state["trading_enabled"] = False
        app.state.shared_state["trading_mode"] = "forward"

        print("Bot components initialized successfully!")
    except Exception as e:
        print(f"Error initializing components: {e}")
        # Continue startup even if components fail to initialize
        app.state.shared_state = {
            "components": {},
            "trading_enabled": False,
            "trading_mode": "forward"
        }

@app.on_event("shutdown")
async def shutdown_event():
    """Clean shutdown procedures"""
    try:
        print("🛑 Initiating clean shutdown...")

        # Mark clean shutdown in self-healing system
        try:
            from app.self_healing_memory import self_healing_memory
            self_healing_memory._mark_clean_shutdown()
        except Exception as e:
            print(f"⚠️ Error marking clean shutdown: {e}")

        # Stop any running processes
        if hasattr(app.state, 'shared_state') and app.state.shared_state:
            components = app.state.shared_state.get("components", {})

            # Shutdown MT5 connections if available
            if "dual_mt5_manager" in components:
                try:
                    components["dual_mt5_manager"].shutdown_terminals()
                except Exception as e:
                    print(f"⚠️ Error shutting down MT5: {e}")

        print("✅ Clean shutdown completed")

    except Exception as e:
        print(f"❌ Error during shutdown: {e}")

# --- Memory storage ---
backtest_memory = PostgresMemory()

# Request/Response models
class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    voice_enabled: bool

class MemoryQueryRequest(BaseModel):
    query: Optional[str] = None
    type_: Optional[str] = None
    limit: int = 20

class MemoryQueryResponse(BaseModel):
    results: List[Dict[str, Any]]



class NewsSentimentResponse(BaseModel):
    symbol: str
    decision: str
    score: float
    top_article: dict | None

class ChatSessionRequest(BaseModel):
    title: str
    session_id: Optional[str] = None

class ChatMessageRequest(BaseModel):
    session_id: str
    message: str
    message_type: str = 'user'

class EditMessageRequest(BaseModel):
    message_id: str
    new_message: str

@app.get("/")
async def root():
    return {"status": "NexGen Forex Trading Bot API is running"}

# Note: The 'chat', 'voice', 'memory', and 'fiass' related endpoints
# are left as placeholders assuming they might be powered by components
# not part of the core trading loop. If they need access to the main
# components, they should be updated to use `request.app.state.components`.

@app.post("/chat", response_model=ChatResponse)
async def process_chat(request: Request, chat_request: ChatRequest):
    try:
        chat_agent = request.app.state.shared_state["components"]["chat_agent"]
        voice_agent = request.app.state.shared_state["components"]["voice_agent"]

        # 🎯 ENHANCED: Process the message with full control capabilities
        response_text = chat_agent.process_message(chat_request.message)

        # Check if voice is enabled
        voice_enabled = voice_agent.voice_enabled if voice_agent else False

        # 🎯 ENHANCED: Include additional context for frontend
        return ChatResponse(
            response=response_text,
            voice_enabled=voice_enabled
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing chat message: {str(e)}")

@app.post("/run-reasoning")
async def run_reasoning(request: Request):
    """Run a complete reasoning cycle using FIASS for all configured symbols."""
    try:
        fiass = request.app.state.shared_state["components"]["fiass"]

        # Run reasoning cycle for all symbols
        fiass.run_all()

        return {"status": "Reasoning cycle completed successfully for all symbols"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running reasoning cycle: {str(e)}")



def serialize_for_json(obj):
    """🔄 Convert complex objects to JSON-serializable format with enhanced error handling"""
    import pandas as pd
    import numpy as np
    from datetime import datetime, date

    try:
        # Handle None and basic types first
        if obj is None:
            return None
        elif isinstance(obj, (str, int, float, bool)):
            return obj
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()

        # Handle pandas objects with extra safety
        elif isinstance(obj, pd.Series):
            try:
                # Try to convert to list first (safer for JSON)
                if obj.empty:
                    return []
                return obj.tolist()
            except:
                try:
                    return obj.to_dict()
                except:
                    return str(obj)
        elif isinstance(obj, pd.DataFrame):
            try:
                if obj.empty:
                    return []
                return obj.to_dict('records')
            except:
                try:
                    return obj.to_dict()
                except:
                    return str(obj)

        # Handle numpy objects
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            try:
                return obj.tolist()
            except:
                return str(obj)
        elif hasattr(obj, 'dtype') and 'float' in str(obj.dtype):
            return float(obj)
        elif hasattr(obj, 'dtype') and 'int' in str(obj.dtype):
            return int(obj)

        # Handle collections
        elif isinstance(obj, dict):
            return {str(k): serialize_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [serialize_for_json(item) for item in obj]

        # Handle objects with to_dict method
        elif hasattr(obj, 'to_dict'):
            try:
                return serialize_for_json(obj.to_dict())
            except:
                return str(obj)

        # Handle objects with __dict__
        elif hasattr(obj, '__dict__'):
            try:
                return serialize_for_json(obj.__dict__)
            except:
                return str(obj)

        # Fallback to string representation
        else:
            return str(obj)

    except Exception as e:
        # Ultimate fallback - return string representation
        print(f"⚠️ Serialization warning for {type(obj)}: {e}")
        return str(obj)

@app.post("/test_simple")
async def test_simple():
    """🧪 Simple test endpoint to isolate the issue"""
    return {"status": "success", "message": "Simple test endpoint working"}



@app.get("/backtest_results")
async def get_backtest_results(limit: int = 20):
    """Returns recent completed backtests and the status of any backtest currently running.
    The response schema matches what the frontend BacktestPanel expects.
    """
    try:
        # Fetch recent backtest logs from PostgresMemory
        logs = backtest_memory.retrieve_memory(type_="BACKTEST", limit=limit)
        completed_backtests = []
        for log in logs:
            meta = log.get("meta") or {}
            # Flatten the structure to what the UI expects
            completed_backtests.append({
                "symbol": meta.get("symbol") or "N/A",
                "strategy": meta.get("strategy") or "Hybrid",
                "timestamp": meta.get("timestamp") or log["timestamp"].isoformat() if isinstance(log.get("timestamp"), str) else str(log.get("timestamp")),
                "performance": meta.get("performance") or {},
                "equity_curve": meta.get("equity_curve") or [],
                "trades": meta.get("trades") or [],
                "ai_assessment": meta.get("ai_assessment") or "Pending Analysis",
            })

        status = "idle"  # Backtesting removed
        return {
            "status": status,
            "current_backtest": None,
            "completed_backtests": completed_backtests,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching backtest results: {str(e)}")




@app.get("/account_stats")
async def get_account_stats():
    """Get live account statistics for real trading account"""
    try:
        info = mt5_manager.get_account_info('live')
        if info is None:
            raise HTTPException(status_code=500, detail="Failed to connect to live MT5 account")
        return info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching live account stats: {str(e)}")

@app.get("/open_trades")
async def get_open_trades():
    """Get open trades from live trading account"""
    try:
        trades = mt5_manager.get_open_trades('live')
        if trades is None:
            raise HTTPException(status_code=500, detail="Failed to connect to live MT5 account")
        return trades
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching live open trades: {str(e)}")

@app.get("/demo_account_stats")
async def get_demo_account_stats():
    """🚀 Get enhanced demo account statistics with real margin data"""
    try:
        info = mt5_manager.get_account_info('demo')
        if info is None:
            raise HTTPException(status_code=500, detail="Failed to connect to demo MT5 account")

        # Ensure margin level is calculated properly
        if 'margin' in info and 'equity' in info and info['margin'] > 0:
            info['margin_level'] = (info['equity'] / info['margin']) * 100
        else:
            info['margin_level'] = 0

        # Add additional calculated fields
        if 'balance' in info and 'equity' in info:
            info['floating_pnl'] = info['equity'] - info['balance']

        return info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching demo account stats: {str(e)}")

@app.get("/demo_open_trades")
async def get_demo_open_trades():
    """Get open trades from demo trading account"""
    try:
        trades = mt5_manager.get_open_trades('demo')
        if trades is None:
            raise HTTPException(status_code=500, detail="Failed to connect to demo MT5 account")
        return trades
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching demo open trades: {str(e)}")

@app.get("/system_health")
async def get_system_health():
    try:
        cpu = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory().percent
        uptime_seconds = int(time.time() - start_time)
        uptime = f"{uptime_seconds // 3600}h {(uptime_seconds % 3600) // 60}m"
        return {
            "cpu": cpu,
            "memory": memory,
            "uptime": uptime,
            "platform": platform.system(),
            "platform_version": platform.version()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching system health: {str(e)}")



@app.get("/ai_decisions")
async def get_ai_decisions(request: Request):
    """Get recent AI decision log entries"""
    try:
        # Get decision log from shared state
        decision_log = request.app.state.shared_state.get("decision_log", [])

        # If no decisions yet, return empty list
        if not decision_log:
            return {"decisions": []}

        # Return the most recent 20 decisions
        return {"decisions": decision_log[:20]}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting AI decisions: {str(e)}")

@app.get("/ultimate_risk_status")
async def get_ultimate_risk_status(request: Request):
    """🛡️ Get Ultimate Risk Manager status - THE BEST BOT'S PROTECTION SYSTEM"""
    try:
        components = request.app.state.shared_state.get("components", {})
        ultimate_risk_manager = components.get("ultimate_risk_manager")

        if not ultimate_risk_manager:
            return {"error": "Ultimate Risk Manager not available"}

        # Get comprehensive risk status
        risk_status = ultimate_risk_manager.get_risk_status()

        # Add AI brain intelligence report if available
        hybrid_brain = components.get("hybrid_brain")
        if hybrid_brain:
            intelligence_report = hybrid_brain.get_intelligence_report()
            risk_status.update(intelligence_report)

        return {
            "status": "active",
            "protection_level": "ULTIMATE",
            **risk_status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting Ultimate Risk status: {str(e)}")



# --- Configuration Endpoints ---

@app.get("/config", response_model=Config)
async def get_config():
    """Returns the current bot configuration."""
    return config

@app.post("/config", response_model=Config)
async def update_config(new_config: Config, request: Request):
    """Updates the bot's configuration."""
    global config
    try:
        # Update the global config object
        for key, value in new_config.dict().items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        # Note: This updates the config in memory. 
        # For persistence across restarts, the config would need to be saved to a file.
        # This simple in-memory update is sufficient for live adjustments.
        
        # Re-initialize components that depend on the config
        # This is a simplified example. A more robust implementation might
        # re-initialize specific components based on what changed.
        request.app.state.shared_state["components"] = initialize_components()

        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating configuration: {str(e)}")

# --- Bot Control Endpoints ---

@app.get("/bot/status")
async def get_bot_status(request: Request):
    is_enabled = request.app.state.shared_state.get("trading_enabled", False)
    return {"status": "running" if is_enabled else "stopped"}

@app.post("/bot/start")
async def start_bot(request: Request):
    request.app.state.shared_state["trading_enabled"] = True
    return {"message": "Bot started."}

@app.post("/bot/stop")
async def stop_bot(request: Request):
    request.app.state.shared_state["trading_enabled"] = False
    return {"message": "Bot stopped. It will finish the current cycle if one is running."}

@app.get("/scheduler/health")
async def get_scheduler_health():
    """Get scheduler health status and statistics"""
    try:
        # Import the health function from run.py
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from run import get_scheduler_health

        health = get_scheduler_health()
        return {"status": "success", "data": health}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/scheduler/stats")
async def get_scheduler_stats():
    """Get detailed scheduler statistics"""
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from run import scheduler_stats

        return {"status": "success", "data": scheduler_stats}
    except Exception as e:
        return {"status": "error", "message": str(e)}



# --- Data Endpoints ---

class TradeLogResponse(BaseModel):
    logs: List[Dict[str, Any]]

@app.get("/trade_logs", response_model=TradeLogResponse)
async def get_trade_logs(request: Request, limit: int = 100):
    try:
        db = request.app.state.shared_state["components"]["db"]
        # Assuming the db object has a method 'get_trade_logs'
        logs = db.get_trade_logs(limit=limit)
        return {"logs": logs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching trade logs: {str(e)}")

@app.post("/clear_trade_logs")
async def clear_trade_logs(request: Request):
    """🗑️ Clear all trade logs from the database"""
    try:
        db = request.app.state.shared_state["components"]["db"]

        with db.conn.cursor() as cur:
            # Get counts before clearing
            cur.execute("SELECT COUNT(*) FROM trades")
            trade_count = cur.fetchone()[0]

            cur.execute("SELECT COUNT(*) FROM indicators")
            indicator_count = cur.fetchone()[0]

            # Clear in correct order (respecting foreign key constraints)
            cur.execute("DELETE FROM indicators")
            deleted_indicators = cur.rowcount

            cur.execute("DELETE FROM llm_reasoning_logs WHERE trade_id IS NOT NULL")
            deleted_reasoning = cur.rowcount

            cur.execute("DELETE FROM trades")
            deleted_trades = cur.rowcount

            cur.execute("DELETE FROM system_states")
            deleted_system = cur.rowcount

            # Reset sequences
            cur.execute("ALTER SEQUENCE trades_id_seq RESTART WITH 1")
            cur.execute("ALTER SEQUENCE indicators_id_seq RESTART WITH 1")
            cur.execute("ALTER SEQUENCE system_states_id_seq RESTART WITH 1")
            cur.execute("ALTER SEQUENCE llm_reasoning_logs_id_seq RESTART WITH 1")

            db.conn.commit()

        return {
            "success": True,
            "message": "Trade logs cleared successfully",
            "cleared": {
                "trades": deleted_trades,
                "indicators": deleted_indicators,
                "reasoning_logs": deleted_reasoning,
                "system_states": deleted_system
            }
        }
    except Exception as e:
        db.conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error clearing trade logs: {str(e)}")

class PriceDataRequest(BaseModel):
    symbol: str
    timeframe: str
    limit: int

@app.post("/price_data")
async def get_price_data(request: Request, data_request: PriceDataRequest):
    try:
        feeder = request.app.state.shared_state["components"]["feeder"]

        # Map common timeframe strings to MT5 constants
        timeframe_map = {
            "M1": mt5.TIMEFRAME_M1,
            "M5": mt5.TIMEFRAME_M5,
            "M15": mt5.TIMEFRAME_M15,
            "M30": mt5.TIMEFRAME_M30,
            "H1": mt5.TIMEFRAME_H1,
            "H4": mt5.TIMEFRAME_H4,
            "D1": mt5.TIMEFRAME_D1,
            "W1": mt5.TIMEFRAME_W1,
            "MN1": mt5.TIMEFRAME_MN1
        }

        timeframe_str = data_request.timeframe.upper()
        if timeframe_str in timeframe_map:
            timeframe = timeframe_map[timeframe_str]
        else:
            # Fallback to getattr method
            try:
                timeframe = getattr(mt5, f"TIMEFRAME_{timeframe_str}")
            except AttributeError:
                raise HTTPException(status_code=400, detail=f"Invalid timeframe: {data_request.timeframe}")

        # Fetch the data
        df = feeder.fetch_data(
            data_request.symbol,
            timeframe=timeframe,
            num_candles=data_request.limit
        )

        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data available for {data_request.symbol}")

        # Convert to format expected by frontend (with time as timestamp)
        result = df.reset_index().to_dict(orient='records')

        # Ensure time is in the correct format for the chart
        for record in result:
            if 'time' in record:
                # Convert to Unix timestamp for lightweight-charts
                record['time'] = int(record['time'].timestamp())

        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching price data: {str(e)}")

# --- Explainability Endpoints ---
@app.get("/explanations/{trade_id}")
async def get_trade_explanation(trade_id: int, request: Request):
    """Fetches (or lazily generates) the explanation for a specific trade."""
    try:
        components = request.app.state.shared_state["components"]
        db = components["db"]
        explanation = db.get_explanation_for_trade(trade_id)
        if explanation:
            return explanation
        # Not found – generate now
        ml_brain = components["ml_brain"]
        explain_engine: ExplainabilityEngine = components["explain_engine"]
        # Fetch trade row and indicator data
        trade_row = db.conn.execute("SELECT * FROM trades WHERE id = ?", (trade_id,)).fetchone()
        if not trade_row:
            raise HTTPException(status_code=404, detail="Trade not found.")
        symbol = trade_row["symbol"]
        latest_indicators_row = db.conn.execute(
            "SELECT * FROM indicator_values WHERE trade_id = ? ORDER BY id DESC LIMIT 1", (trade_id,)
        ).fetchone()
        if not latest_indicators_row:
            raise HTTPException(status_code=404, detail="Indicators not found for this trade.")
        features_dict = dict(latest_indicators_row)
        # Create DataFrame from the features dictionary
        import pandas as pd
        features_raw_df = pd.DataFrame([features_dict])
        features_df = ml_brain.features_from_df(features_raw_df)
        explain_engine.explain_trade(
            trade_id=trade_id,
            features=features_df.iloc[-1].to_dict(),
            X_row=features_df.values[-1:],  # Ensure it's a 2D array with one row
            model_confidence=trade_row["confidence"] or 0.0,
            action=trade_row["action"],
            symbol=symbol,
        )
        # Retrieve new explanation
        explanation = db.get_explanation_for_trade(trade_id)
        if explanation:
            return explanation
        else:
            raise HTTPException(status_code=500, detail="Failed to generate explanation.")
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching explanation: {str(e)}")

# --- News Endpoint ---
@app.get("/news")
async def get_news_events(request: Request):
    """Fetches upcoming economic news events."""
    try:
        news_filter = request.app.state.shared_state["components"]["news_filter"]
        events = news_filter.get_todays_events()
        
        # Also check which events are currently blocking trades
        now_utc = datetime.utcnow()
        for event in events:
            event['isBlocked'] = False
            if event['impact'] in ['high', 'medium']:
                try:
                    # Handle different time formats
                    time_str = event.get('time', '')
                    if len(time_str) == 5:  # Format: "HH:MM"
                        event_time_utc = datetime.strptime(f"{date.today().isoformat()} {time_str}", '%Y-%m-%d %H:%M')
                    else:  # Format: "YYYY-MM-DD HH:MM"
                        event_time_utc = datetime.strptime(time_str, '%Y-%m-%d %H:%M')

                    time_before = event_time_utc - news_filter.buffer
                    time_after = event_time_utc + news_filter.buffer
                    if time_before <= now_utc <= time_after:
                        event['isBlocked'] = True
                except (ValueError, TypeError) as e:
                    # Skip events with invalid time formats
                    print(f"[API] Invalid time format for event {event.get('event', 'Unknown')}: {time_str}")
                    continue
        
        return {"events": events}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching news events: {str(e)}")

# --- Anomaly Detection Endpoint ---
@app.get("/anomaly_status")
async def get_anomaly_status(request: Request):
    """Fetches the latest market anomaly analysis."""
    try:
        anomaly_detector = request.app.state.shared_state["components"]["anomaly_detector"]
        analysis = anomaly_detector.get_analysis()
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching anomaly status: {str(e)}")

@app.get("/news_sentiment", response_model=NewsSentimentResponse)
async def get_news_sentiment(symbol: str = "EURUSD"):
    """Returns aggregated headline sentiment for the given forex pair."""
    try:
        decision = run_sentiment_strategy(symbol)
        # run_sentiment_strategy logs and returns decision; we need avg score & top article
        arts = score_headlines(fetch_news(symbol))
        avg = sum(a.sentiment for a in arts) / len(arts) if arts else 0.0
        return {
            "symbol": symbol,
            "decision": decision,
            "score": round(avg, 3),
            "top_article": arts[0].__dict__ if arts else None,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching sentiment: {str(e)}")

# --- Memory Endpoints ---
@app.post("/memory", response_model=MemoryQueryResponse)
async def query_memory(request: Request, memory_request: MemoryQueryRequest):
    """Query the memory system for stored information."""
    try:
        memory_summarizer = request.app.state.shared_state["components"]["memory_summarizer"]
        memory = memory_summarizer.memory

        if memory_request.query:
            # Search for specific query
            results = memory.search_memory(memory_request.query, limit=memory_request.limit)
        else:
            # Retrieve by type
            results = memory.retrieve_memory(
                type_=memory_request.type_,
                limit=memory_request.limit
            )

        # Convert results to the expected format
        formatted_results = []
        for result in results:
            formatted_results.append({
                "id": result.get("id"),
                "timestamp": result.get("timestamp").isoformat() if hasattr(result.get("timestamp"), "isoformat") else str(result.get("timestamp")),
                "type": result.get("type"),
                "content": result.get("content"),
                "meta": result.get("meta", {})
            })

        return MemoryQueryResponse(results=formatted_results)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error querying memory: {str(e)}")

@app.post("/summarize")
async def summarize_memory(request: Request):
    """Generate a summary of recent memory/trading activity."""
    try:
        memory_summarizer = request.app.state.shared_state["components"]["memory_summarizer"]
        summary = memory_summarizer.summarize_week()
        return {"summary": summary}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error summarizing memory: {str(e)}")

# --- Voice Endpoints ---
@app.post("/voice")
async def generate_voice(
    request: Request,
    text: str,
    voice_id: Optional[str] = None,
    speed: Optional[float] = None,
    pitch: Optional[float] = None,
    volume: Optional[float] = None
):
    """Generate speech from text using the voice agent."""
    try:
        voice_agent = request.app.state.shared_state["components"]["voice_agent"]

        if not voice_agent.voice_enabled:
            return {"status": "Voice output is disabled"}

        # Create a temporary file for the audio
        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_file:
            temp_path = temp_file.name

        try:
            # Generate speech and save to temp file (don't play locally for API calls)
            voice_agent.speak(text, voice_id=voice_id, save_path=temp_path, play_locally=False)

            # Read the audio file and return as response
            if os.path.exists(temp_path):
                with open(temp_path, "rb") as audio_file:
                    audio_data = audio_file.read()

                return Response(
                    content=audio_data,
                    media_type="audio/mpeg",
                    headers={"Content-Disposition": "attachment; filename=speech.mp3"}
                )
            else:
                return {"status": "Audio generation failed"}
        finally:
            # Clean up temp file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating voice: {str(e)}")

@app.post("/transcribe")
async def transcribe_audio(audio: UploadFile = File(...)):
    """Transcribe audio to text."""
    try:
        # For now, return a placeholder response
        # In a real implementation, you would use a speech-to-text service
        # like OpenAI Whisper, Google Speech-to-Text, etc.

        # Read the audio file
        audio_data = await audio.read()

        # Placeholder transcription
        # TODO: Implement actual speech-to-text
        return {
            "text": "Transcription not yet implemented. Please implement with Whisper or similar service.",
            "confidence": 0.0
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error transcribing audio: {str(e)}")

@app.get("/voices")
async def get_voice_options(request: Request):
    """Get available voice options."""
    try:
        voice_agent = request.app.state.shared_state["components"]["voice_agent"]

        if voice_agent.use_elevenlabs:
            # Return ElevenLabs voice options
            return {
                "voices": [
                    {"id": "EXAVITQu4vr4xnSDxMaL", "name": "Bella", "preview_url": ""},
                    {"id": "21m00Tcm4TlvDq8ikWAM", "name": "Rachel", "preview_url": ""},
                    {"id": "AZnzlk1XvdvUeBnXmlld", "name": "Domi", "preview_url": ""},
                    {"id": "ErXwobaYiN019PkySvjV", "name": "Antoni", "preview_url": ""},
                    {"id": "VR6AewLTigWG4xSOukaG", "name": "Arnold", "preview_url": ""}
                ]
            }
        else:
            # Return system TTS voices
            return {
                "voices": [
                    {"id": "default", "name": "System Default", "preview_url": ""}
                ]
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching voice options: {str(e)}")

# --- Mode Management Endpoints ---
class ModeRequest(BaseModel):
    mode: str  # "backtest" or "forward"

@app.post("/set_mode")
async def set_mode(request: Request, mode_request: ModeRequest):
    """Set the trading mode (backtest or forward)."""
    try:
        if mode_request.mode not in ["backtest", "forward"]:
            raise HTTPException(status_code=400, detail="Mode must be 'backtest' or 'forward'")

        # Store the mode in shared state
        request.app.state.shared_state["trading_mode"] = mode_request.mode

        return {"message": f"Trading mode set to {mode_request.mode}", "mode": mode_request.mode}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error setting mode: {str(e)}")

@app.get("/get_mode")
async def get_mode(request: Request):
    """Get the current trading mode."""
    try:
        current_mode = request.app.state.shared_state.get("trading_mode", "forward")
        return {"mode": current_mode}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting mode: {str(e)}")

# --- Enhanced Voice Control Endpoints ---
@app.post("/voice/start_control")
async def start_voice_control(request: Request):
    """🎤 Start voice control system"""
    try:
        voice_agent = request.app.state.shared_state["components"]["voice_agent"]

        if not voice_agent.speech_recognition_available:
            return {
                "status": "error",
                "message": "Speech recognition not available. Install: pip install SpeechRecognition pyaudio"
            }

        success = voice_agent.start_voice_control()

        if success:
            return {
                "status": "success",
                "message": "Voice control activated! Say 'hey bot' to start.",
                "listening": True
            }
        else:
            return {
                "status": "error",
                "message": "Failed to start voice control"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting voice control: {str(e)}")

@app.post("/voice/stop_control")
async def stop_voice_control(request: Request):
    """🔇 Stop voice control system"""
    try:
        voice_agent = request.app.state.shared_state["components"]["voice_agent"]
        voice_agent.stop_voice_control()

        return {
            "status": "success",
            "message": "Voice control deactivated.",
            "listening": False
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error stopping voice control: {str(e)}")

@app.get("/voice/status")
async def get_voice_status(request: Request):
    """🎤 Get Whisper voice control status"""
    try:
        voice_agent = request.app.state.shared_state["components"]["voice_agent"]

        # Use the new get_voice_status method if available (Whisper agent)
        if hasattr(voice_agent, 'get_voice_status'):
            return voice_agent.get_voice_status()

        # Fallback for old voice agent
        return {
            "voice_enabled": voice_agent.voice_enabled,
            "speech_recognition_available": voice_agent.speech_recognition_available,
            "listening": getattr(voice_agent, 'listening', False),
            "elevenlabs_available": getattr(voice_agent, 'use_elevenlabs', False),
            "local_tts_available": voice_agent.engine is not None,
            "whisper_enabled": False,
            "whisper_model": None
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting voice status: {str(e)}")

# --- Chat History Endpoints ---
@app.post("/chat/sessions")
async def create_chat_session(session_request: ChatSessionRequest):
    """Create a new chat session"""
    try:
        from app.database import db
        import uuid

        # Use provided session_id if available, otherwise generate new one
        session_id = getattr(session_request, 'session_id', None) or f"session_{uuid.uuid4().hex[:12]}"
        success = db.create_chat_session(session_id, session_request.title)

        if success:
            return {"session_id": session_id, "title": session_request.title, "status": "created"}
        else:
            raise HTTPException(status_code=500, detail="Failed to create chat session")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating chat session: {str(e)}")

@app.get("/chat/sessions")
async def get_chat_sessions():
    """Get all chat sessions"""
    try:
        from app.database import db
        sessions = db.get_chat_sessions()
        return {"sessions": sessions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting chat sessions: {str(e)}")

@app.get("/chat/sessions/{session_id}")
async def get_chat_session(session_id: str):
    """Get a specific chat session with messages"""
    try:
        from app.database import db
        session = db.get_chat_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")

        messages = db.get_chat_messages(session_id)
        session['messages'] = messages
        return session
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting chat session: {str(e)}")

@app.delete("/chat/sessions/{session_id}")
async def delete_chat_session(session_id: str):
    """Delete a chat session"""
    try:
        from app.database import db
        success = db.delete_chat_session(session_id)
        if success:
            return {"status": "deleted"}
        else:
            raise HTTPException(status_code=404, detail="Chat session not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting chat session: {str(e)}")

@app.post("/chat/cleanup")
async def cleanup_chat_sessions():
    """🧹 Clean up duplicate and empty chat sessions"""
    try:
        from app.database import db

        # Get all sessions with message counts
        sessions = db.get_all_chat_sessions()
        sessions_with_counts = []

        for session in sessions:
            messages = db.get_chat_messages(session['id'])
            sessions_with_counts.append({
                **session,
                'message_count': len(messages)
            })

        # Separate sessions with messages vs empty ones
        sessions_with_messages = [s for s in sessions_with_counts if s['message_count'] > 0]
        empty_sessions = [s for s in sessions_with_counts if s['message_count'] == 0]

        # Keep all sessions with messages + only the most recent empty session
        sessions_to_keep = sessions_with_messages
        if empty_sessions:
            # Sort empty sessions by created_at and keep the most recent one
            empty_sessions.sort(key=lambda x: x['created_at'], reverse=True)
            sessions_to_keep.append(empty_sessions[0])

        # Delete the sessions we don't want to keep
        sessions_to_delete = [s for s in sessions_with_counts if s not in sessions_to_keep]
        deleted_count = 0

        for session in sessions_to_delete:
            if db.delete_chat_session(session['id']):
                deleted_count += 1

        return {
            "status": "cleanup_complete",
            "total_sessions": len(sessions_with_counts),
            "sessions_with_messages": len(sessions_with_messages),
            "empty_sessions": len(empty_sessions),
            "sessions_kept": len(sessions_to_keep),
            "sessions_deleted": deleted_count
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error during cleanup: {str(e)}")

@app.post("/chat/messages")
async def add_chat_message(message_request: ChatMessageRequest):
    """Add a message to a chat session"""
    try:
        from app.database import db
        import uuid

        message_id = f"msg_{uuid.uuid4().hex[:12]}"
        success = db.add_chat_message(
            message_id,
            message_request.session_id,
            message_request.message_type,
            message_request.message
        )

        if success:
            return {"message_id": message_id, "status": "added"}
        else:
            raise HTTPException(status_code=500, detail="Failed to add message")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error adding chat message: {str(e)}")

@app.put("/chat/messages/edit")
async def edit_chat_message(edit_request: EditMessageRequest):
    """Edit a chat message"""
    try:
        from app.database import db
        success = db.edit_chat_message(edit_request.message_id, edit_request.new_message)

        if success:
            return {"status": "edited"}
        else:
            raise HTTPException(status_code=404, detail="Message not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error editing chat message: {str(e)}")

@app.delete("/chat/messages/{message_id}")
async def delete_chat_message(message_id: str):
    """Delete a chat message"""
    try:
        from app.database import db
        success = db.delete_chat_message(message_id)

        if success:
            return {"status": "deleted"}
        else:
            raise HTTPException(status_code=404, detail="Message not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting chat message: {str(e)}")

# 🧠 PHASE 1: LLM REASONING & INTELLIGENCE ENDPOINTS

@app.get("/reasoning/analysis")
async def get_reasoning_analysis():
    """Get LLM reasoning analysis"""
    try:
        components = app.state.shared_state.get("components", {})
        reasoning_analyzer = components.get("reasoning_analyzer")

        if reasoning_analyzer:
            analysis = reasoning_analyzer.get_recent_analysis()
            return {"status": "success", "analysis": analysis}
        else:
            return {"status": "error", "message": "Reasoning analyzer not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting reasoning analysis: {str(e)}")

@app.get("/reasoning/quality-metrics")
async def get_reasoning_quality_metrics():
    """Get reasoning quality metrics"""
    try:
        components = app.state.shared_state.get("components", {})
        quality_metrics = components.get("reasoning_quality_metrics")

        if quality_metrics:
            metrics = quality_metrics.get_quality_metrics()
            return {"status": "success", "metrics": metrics}
        else:
            return {"status": "error", "message": "Quality metrics not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting quality metrics: {str(e)}")

# 🤖 PHASE 2: AUTONOMOUS LEARNING ENDPOINTS

@app.get("/autonomous/performance-patterns")
async def get_performance_patterns():
    """Get detected performance patterns"""
    try:
        components = app.state.shared_state.get("components", {})
        performance_detector = components.get("performance_detector")

        if performance_detector:
            patterns = performance_detector.get_detected_patterns()
            return {"status": "success", "patterns": patterns}
        else:
            return {"status": "error", "message": "Performance detector not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting performance patterns: {str(e)}")

@app.get("/autonomous/parameter-tuning")
async def get_parameter_tuning_status():
    """Get parameter auto-tuning status"""
    try:
        components = app.state.shared_state.get("components", {})
        parameter_tuner = components.get("parameter_tuner")

        if parameter_tuner:
            status = parameter_tuner.get_tuning_status()
            return {"status": "success", "tuning_status": status}
        else:
            return {"status": "error", "message": "Parameter tuner not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting tuning status: {str(e)}")

@app.get("/autonomous/confidence-calibration")
async def get_confidence_calibration():
    """Get confidence calibration data"""
    try:
        components = app.state.shared_state.get("components", {})
        confidence_calibrator = components.get("confidence_calibrator")

        if confidence_calibrator:
            calibration = confidence_calibrator.get_calibration_data()
            return {"status": "success", "calibration": calibration}
        else:
            return {"status": "error", "message": "Confidence calibrator not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting confidence calibration: {str(e)}")

@app.get("/autonomous/learning-feedback")
async def get_learning_feedback():
    """Get learning feedback loop status"""
    try:
        components = app.state.shared_state.get("components", {})
        learning_loop = components.get("learning_loop")

        if learning_loop:
            feedback = learning_loop.get_feedback_status()
            return {"status": "success", "feedback": feedback}
        else:
            return {"status": "error", "message": "Learning loop not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting learning feedback: {str(e)}")

# 👥 PHASE 3: HUMAN-AI COLLABORATION ENDPOINTS

@app.get("/collaboration/status")
async def get_collaboration_status():
    """Get human-AI collaboration status"""
    try:
        components = app.state.shared_state.get("components", {})
        human_ai_collab = components.get("human_ai_collab")

        if human_ai_collab:
            status = human_ai_collab.get_collaboration_status()
            return {"status": "success", "collaboration": status}
        else:
            return {"status": "error", "message": "Human-AI collaboration not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting collaboration status: {str(e)}")

@app.get("/collaboration/pending-approvals")
async def get_pending_approvals():
    """Get pending approval requests"""
    try:
        components = app.state.shared_state.get("components", {})
        approval_workflow = components.get("approval_workflow")

        if approval_workflow:
            approvals = approval_workflow.get_pending_approvals()
            return {"status": "success", "pending_approvals": approvals}
        else:
            return {"status": "error", "message": "Approval workflow not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting pending approvals: {str(e)}")

@app.post("/collaboration/approve/{approval_id}")
async def approve_request(approval_id: str, approved: bool):
    """Approve or reject a pending request"""
    try:
        components = app.state.shared_state.get("components", {})
        approval_workflow = components.get("approval_workflow")

        if approval_workflow:
            result = approval_workflow.process_approval(approval_id, approved)
            return {"status": "success", "result": result}
        else:
            return {"status": "error", "message": "Approval workflow not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing approval: {str(e)}")

# 🎯 AUTONOMOUS SYSTEM ORCHESTRATOR ENDPOINTS

@app.get("/autonomous/system-status")
async def get_autonomous_system_status():
    """Get autonomous trading system status"""
    try:
        components = app.state.shared_state.get("components", {})
        autonomous_system = components.get("autonomous_system")

        if autonomous_system:
            status = autonomous_system.get_system_status()
            return {"status": "success", "system_status": status}
        else:
            return {"status": "error", "message": "Autonomous system not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system status: {str(e)}")

@app.post("/autonomous/toggle-mode")
async def toggle_autonomous_mode(enabled: bool):
    """Toggle autonomous trading mode"""
    try:
        components = app.state.shared_state.get("components", {})
        autonomous_system = components.get("autonomous_system")

        if autonomous_system:
            result = autonomous_system.set_autonomous_mode(enabled)
            return {"status": "success", "autonomous_mode": result}
        else:
            return {"status": "error", "message": "Autonomous system not available"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error toggling autonomous mode: {str(e)}")

@app.get("/advanced/system-overview")
async def get_advanced_system_overview():
    """Get comprehensive overview of all advanced systems"""
    try:
        components = app.state.shared_state.get("components", {})

        overview = {
            "phase_1_reasoning": {
                "reasoning_analyzer": bool(components.get("reasoning_analyzer")),
                "quality_metrics": bool(components.get("reasoning_quality_metrics"))
            },
            "phase_2_autonomous": {
                "performance_detector": bool(components.get("performance_detector")),
                "parameter_tuner": bool(components.get("parameter_tuner")),
                "confidence_calibrator": bool(components.get("confidence_calibrator")),
                "learning_loop": bool(components.get("learning_loop"))
            },
            "phase_3_collaboration": {
                "human_ai_collab": bool(components.get("human_ai_collab")),
                "approval_workflow": bool(components.get("approval_workflow"))
            },
            "autonomous_orchestrator": {
                "autonomous_system": bool(components.get("autonomous_system"))
            },
            "total_components": len(components),
            "advanced_features_active": True
        }

        return {"status": "success", "overview": overview}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system overview: {str(e)}")


# 🚀 VectorBT Backtesting Endpoints

class BacktestRequest(BaseModel):
    symbol: str = "EURUSD"
    strategy: str = "ma_crossover"
    initial_cash: float = 10000
    num_candles: int = 1000
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    use_date_range: bool = False

@app.post("/api/backtest/run")
async def run_vectorbt_backtest(request: BacktestRequest):
    """🚀 Run VectorBT backtest"""
    try:
        if request.use_date_range and request.start_date and request.end_date:
            results = vectorbt_service.run_backtest_with_dates(
                symbol=request.symbol,
                strategy=request.strategy,
                initial_cash=request.initial_cash,
                start_date=request.start_date,
                end_date=request.end_date
            )
        else:
            results = vectorbt_service.run_backtest(
                symbol=request.symbol,
                strategy=request.strategy,
                initial_cash=request.initial_cash,
                num_candles=request.num_candles
            )
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backtest failed: {str(e)}")

@app.get("/api/backtest/plot")
async def serve_backtest_plot():
    """📈 Serve Plotly chart as HTML"""
    try:
        chart_html = vectorbt_service.generate_chart()
        return HTMLResponse(content=chart_html, status_code=200)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chart generation failed: {str(e)}")

@app.get("/api/backtest/stats")
async def get_backtest_stats():
    """📊 Get detailed portfolio statistics"""
    try:
        stats = vectorbt_service.get_portfolio_stats()
        return {"stats": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Stats retrieval failed: {str(e)}")

@app.get("/api/backtest/trades")
async def get_backtest_trades():
    """📋 Get individual trades data"""
    try:
        trades = vectorbt_service.get_trades_data()
        return {"trades": trades}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Trades retrieval failed: {str(e)}")

@app.get("/api/backtest/strategies")
async def get_available_strategies():
    """📋 Get available trading strategies"""
    try:
        strategies = vectorbt_service.get_available_strategies()
        return {"strategies": strategies}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Strategies retrieval failed: {str(e)}")

# 🦢 BLACK SWAN DETECTION & RISK GUARD ENDPOINTS

@app.get("/api/black-swan/status")
async def get_black_swan_status():
    """🦢 Get Black Swan detection status"""
    try:
        from app.black_swan_detector import black_swan_detector
        return black_swan_detector.get_risk_status()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting Black Swan status: {str(e)}")

@app.post("/api/black-swan/detect")
async def trigger_black_swan_detection():
    """🦢 Manually trigger Black Swan detection"""
    try:
        from app.black_swan_detector import black_swan_detector
        detected_events = black_swan_detector.detect_black_swan_events()

        return {
            "status": "completed",
            "events_detected": len(detected_events),
            "events": {symbol: {
                "severity": event.severity.value,
                "confidence": event.confidence,
                "recommended_actions": event.recommended_actions,
                "risk_override": event.risk_override
            } for symbol, event in detected_events.items()}
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in Black Swan detection: {str(e)}")

@app.post("/api/black-swan/emergency-stop")
async def force_emergency_stop():
    """🚨 Force emergency stop for all trading"""
    try:
        from app.black_swan_detector import black_swan_detector
        black_swan_detector.force_emergency_stop("Manual API trigger")

        return {
            "status": "emergency_stop_activated",
            "message": "All trading halted due to manual emergency stop",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error forcing emergency stop: {str(e)}")

@app.post("/api/black-swan/clear-emergency")
async def clear_emergency_stop():
    """✅ Clear emergency stop and resume trading"""
    try:
        from app.black_swan_detector import black_swan_detector
        black_swan_detector.clear_emergency_stop()

        return {
            "status": "emergency_stop_cleared",
            "message": "Trading operations resumed",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error clearing emergency stop: {str(e)}")

# 🧠 MEMORY-RAG SYSTEM ENDPOINTS

@app.get("/api/memory-rag/status")
async def get_memory_rag_status():
    """🧠 Get Memory-RAG system status"""
    try:
        from app.memory_rag_system import memory_rag_system
        stats = memory_rag_system.get_memory_statistics()
        return {
            "status": "active",
            "memory_stats": stats,
            "last_index_update": stats.get('last_index_update'),
            "index_size": stats.get('index_size', 0)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting Memory-RAG status: {str(e)}")

@app.post("/api/memory-rag/insights/{symbol}")
async def get_memory_rag_insights(symbol: str):
    """🧠 Get Memory-RAG insights for trading decision"""
    try:
        from app.memory_rag_system import memory_rag_system

        # Create sample situation (in real use, this would come from current market data)
        current_situation = {
            'symbol': symbol,
            'action': 'analyze',
            'market_conditions': {'trend': 'bullish', 'volatility': 'normal'},
            'reasoning': 'API request for insights'
        }

        insights = memory_rag_system.get_rag_enhanced_decision(current_situation)
        return insights
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting Memory-RAG insights: {str(e)}")

# 🎯 ZONE DETECTION ENDPOINTS

@app.get("/api/zones/{symbol}")
async def get_zones_for_symbol(symbol: str):
    """🎯 Get detected zones for symbol"""
    try:
        from app.zone_detection_system import zone_detection_system
        zones = zone_detection_system.detect_zones_for_symbol(symbol)

        return {
            "symbol": symbol,
            "zones_count": len(zones),
            "zones": [zone_detection_system._zone_to_dict(zone) for zone in zones],
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting zones: {str(e)}")

@app.get("/api/zones/active")
async def get_all_active_zones():
    """🎯 Get all active zones across symbols"""
    try:
        from app.zone_detection_system import zone_detection_system

        all_zones = {}
        total_zones = 0

        for symbol in config.SYMBOLS:
            zones = zone_detection_system.active_zones.get(symbol, [])
            all_zones[symbol] = [zone_detection_system._zone_to_dict(zone) for zone in zones]
            total_zones += len(zones)

        return {
            "total_zones": total_zones,
            "zones_by_symbol": all_zones,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting active zones: {str(e)}")

# 📊 MARKET REGIME ENDPOINTS

@app.get("/api/regime/{symbol}")
async def get_market_regime(symbol: str):
    """📊 Get market regime analysis for symbol"""
    try:
        from app.market_regime_detector import market_regime_detector
        recommendations = market_regime_detector.get_trading_recommendations(symbol)
        return recommendations
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting market regime: {str(e)}")

@app.get("/api/regime/all")
async def get_all_market_regimes():
    """📊 Get market regime analysis for all symbols"""
    try:
        from app.market_regime_detector import market_regime_detector

        all_regimes = {}
        for symbol in config.SYMBOLS:
            try:
                regime = market_regime_detector.get_regime_for_symbol(symbol)
                if regime:
                    all_regimes[symbol] = {
                        'market_regime': regime.market_regime.value,
                        'confidence': regime.regime_confidence,
                        'recommended_strategy': regime.recommended_strategy,
                        'risk_level': market_regime_detector._assess_risk_level(regime)
                    }
            except Exception as e:
                all_regimes[symbol] = {'error': str(e)}

        # Get overall statistics
        stats = market_regime_detector.get_regime_statistics()

        return {
            "regimes_by_symbol": all_regimes,
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting all market regimes: {str(e)}")

@app.get("/api/regime/statistics")
async def get_regime_statistics():
    """📊 Get market regime detection statistics"""
    try:
        from app.market_regime_detector import market_regime_detector
        stats = market_regime_detector.get_regime_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting regime statistics: {str(e)}")

# 🛡️ SELF-HEALING MEMORY SYSTEM ENDPOINTS

@app.get("/api/self-healing/status")
async def get_self_healing_status():
    """🛡️ Get self-healing system status"""
    try:
        from app.self_healing_memory import self_healing_memory
        status = self_healing_memory.get_system_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting self-healing status: {str(e)}")

@app.post("/api/self-healing/backup")
async def create_manual_backup():
    """💾 Create manual backup"""
    try:
        from app.self_healing_memory import self_healing_memory

        backup_info = await self_healing_memory.create_backup("manual")

        return {
            "status": "success" if backup_info.status == backup_info.status.SUCCESS else "failed",
            "backup_id": backup_info.backup_id,
            "file_size": backup_info.file_size,
            "timestamp": backup_info.timestamp.isoformat(),
            "metadata": backup_info.metadata
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating backup: {str(e)}")

@app.get("/api/self-healing/backups")
async def get_backup_history():
    """📂 Get backup history"""
    try:
        from app.self_healing_memory import self_healing_memory

        backups = []
        for backup in self_healing_memory.backup_history:
            backups.append({
                "backup_id": backup.backup_id,
                "timestamp": backup.timestamp.isoformat(),
                "backup_type": backup.backup_type,
                "file_size": backup.file_size,
                "status": backup.status.value,
                "recovery_level": backup.recovery_level.value,
                "metadata": backup.metadata
            })

        return {
            "total_backups": len(backups),
            "backups": backups,
            "last_backup": backups[0] if backups else None
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting backup history: {str(e)}")

@app.post("/api/self-healing/restore/{backup_id}")
async def restore_from_backup(backup_id: str, recovery_level: str = "full_recovery"):
    """🔄 Restore system from backup"""
    try:
        from app.self_healing_memory import self_healing_memory, RecoveryLevel

        # Validate recovery level
        try:
            recovery_enum = RecoveryLevel(recovery_level)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid recovery level: {recovery_level}")

        success = await self_healing_memory.restore_from_backup(backup_id, recovery_enum)

        return {
            "status": "success" if success else "failed",
            "backup_id": backup_id,
            "recovery_level": recovery_level,
            "timestamp": datetime.now().isoformat(),
            "message": "System restored successfully" if success else "Restore operation failed"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error restoring from backup: {str(e)}")

@app.post("/api/self-healing/health-check")
async def trigger_health_check():
    """🔍 Trigger manual health check and corruption detection"""
    try:
        from app.self_healing_memory import self_healing_memory

        healing_success = await self_healing_memory.detect_and_heal_corruption()

        return {
            "status": "healthy" if healing_success else "issues_detected",
            "healing_attempted": True,
            "healing_success": healing_success,
            "timestamp": datetime.now().isoformat(),
            "message": "System is healthy" if healing_success else "Issues detected and healing attempted"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in health check: {str(e)}")

@app.post("/api/self-healing/emergency-backup")
async def create_emergency_backup():
    """🚨 Create emergency backup"""
    try:
        from app.self_healing_memory import self_healing_memory

        backup_info = await self_healing_memory.create_backup("emergency")

        return {
            "status": "success" if backup_info.status == backup_info.status.SUCCESS else "failed",
            "backup_id": backup_info.backup_id,
            "backup_type": "emergency",
            "file_size": backup_info.file_size,
            "timestamp": backup_info.timestamp.isoformat(),
            "message": "Emergency backup created successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating emergency backup: {str(e)}")

@app.post("/api/self-healing/cleanup-backups")
async def cleanup_old_backups():
    """🧹 Clean up old backup files manually"""
    try:
        from app.self_healing_memory import self_healing_memory

        # Get backup count before cleanup
        backups_before = len(self_healing_memory.backup_history)

        # Calculate total size before cleanup
        total_size_before = 0
        for backup in self_healing_memory.backup_history:
            if os.path.exists(backup.file_path):
                total_size_before += os.path.getsize(backup.file_path)

        # Perform cleanup
        self_healing_memory._cleanup_old_backups()

        # Get stats after cleanup
        backups_after = len(self_healing_memory.backup_history)

        # Calculate total size after cleanup
        total_size_after = 0
        for backup in self_healing_memory.backup_history:
            if os.path.exists(backup.file_path):
                total_size_after += os.path.getsize(backup.file_path)

        backups_removed = backups_before - backups_after
        size_freed = total_size_before - total_size_after

        return {
            "status": "success",
            "message": "Backup cleanup completed successfully",
            "backups_before": backups_before,
            "backups_after": backups_after,
            "backups_removed": backups_removed,
            "size_freed_bytes": size_freed,
            "size_freed_formatted": self_healing_memory._format_file_size(size_freed),
            "remaining_backups": [
                {
                    "backup_id": backup.backup_id,
                    "timestamp": backup.timestamp.isoformat(),
                    "backup_type": backup.backup_type,
                    "file_size": backup.file_size,
                    "file_size_formatted": self_healing_memory._format_file_size(backup.file_size)
                }
                for backup in self_healing_memory.backup_history
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error cleaning up backups: {str(e)}")

async def get_backup_record_count(bot_token: str, file_id: str) -> str:
    """📊 Get the actual record count from a backup file without full restore"""
    try:
        import tempfile
        import zipfile
        import json
        import os
        import requests

        # Download the file temporarily
        file_info_url = f"https://api.telegram.org/bot{bot_token}/getFile"
        file_info_response = requests.get(file_info_url, params={'file_id': file_id}, timeout=10)

        if file_info_response.status_code != 200:
            return "Unknown"

        file_info_data = file_info_response.json()
        if not file_info_data.get('ok'):
            return "Unknown"

        file_path = file_info_data['result']['file_path']
        download_url = f"https://api.telegram.org/file/bot{bot_token}/{file_path}"
        download_response = requests.get(download_url, timeout=30)

        if download_response.status_code != 200:
            return "Unknown"

        # Save to temporary file and extract
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
            temp_file.write(download_response.content)
            temp_path = temp_file.name

        try:
            # Extract and count records from the backup ZIP
            with zipfile.ZipFile(temp_path, 'r') as zip_ref:
                # First, try to get record count from metadata.json (most accurate)
                if 'metadata.json' in zip_ref.namelist():
                    try:
                        with zip_ref.open('metadata.json') as f:
                            metadata = json.load(f)
                            memory_records = metadata.get('metadata', {}).get('memory_records')
                            if memory_records is not None:
                                return str(memory_records)
                    except Exception as e:
                        print(f"⚠️ Could not read metadata.json: {e}")

                # Fallback: Try to extract from backup_history.json
                if 'backup_history.json' in zip_ref.namelist():
                    try:
                        with zip_ref.open('backup_history.json') as f:
                            history = json.load(f)
                            if isinstance(history, list) and len(history) > 0:
                                latest_backup = history[-1]  # Get most recent backup
                                memory_records = latest_backup.get('metadata', {}).get('memory_records')
                                if memory_records is not None:
                                    return str(memory_records)
                    except Exception as e:
                        print(f"⚠️ Could not read backup_history.json: {e}")

                # Fallback: Try to count from the actual backup file
                backup_files = [f for f in zip_ref.namelist() if f.endswith('.json.gz')]
                if backup_files:
                    try:
                        import gzip
                        with zip_ref.open(backup_files[0]) as zip_file:
                            with gzip.open(zip_file, 'rt', encoding='utf-8') as gz_file:
                                backup_data = json.load(gz_file)
                                memory_logs = backup_data.get('memory_logs', [])
                                return str(len(memory_logs))
                    except Exception as e:
                        print(f"⚠️ Could not read backup file: {e}")

                return "Unknown"

        finally:
            # Clean up temp file
            try:
                os.unlink(temp_path)
            except:
                pass

    except Exception as e:
        print(f"⚠️ Error getting record count: {str(e)}")
        return "Unknown"

@app.post("/api/self-healing/search-telegram-backups")
async def search_telegram_backups(request: dict):
    """🔍 Search for backup files in Telegram (for Memory Recovery Wizard)"""
    try:
        import requests
        import re
        from datetime import datetime

        print(f"🔍 Memory Recovery: Starting backup search...")

        bot_token = request.get('botToken')
        chat_id = request.get('chatId')
        file_id = request.get('fileId')  # Optional direct file_id
        print(f"🔍 Memory Recovery: Credentials provided - Token: {bot_token[:20] if bot_token else 'None'}..., Chat ID: {chat_id}, File ID: {file_id[:20] if file_id else 'None'}...")

        if not bot_token or not chat_id:
            raise HTTPException(status_code=400, detail="Bot token and chat ID are required")

        # Validate credentials format
        if not re.match(r'^\d+:[A-Za-z0-9_-]+$', bot_token):
            raise HTTPException(status_code=400, detail="Invalid bot token format")

        if not re.match(r'^-?\d+$', chat_id):
            raise HTTPException(status_code=400, detail="Invalid chat ID format")

        print(f"🔍 Memory Recovery: Testing Telegram connection...")
        # Test connection first
        test_url = f"https://api.telegram.org/bot{bot_token}/getMe"
        test_response = requests.get(test_url, timeout=10)

        if test_response.status_code != 200:
            print(f"❌ Memory Recovery: Telegram connection failed - {test_response.status_code}")
            raise HTTPException(status_code=400, detail="Failed to connect to Telegram. Please check your credentials.")

        print(f"✅ Memory Recovery: Telegram connection successful")

        backup_files = []

        # Priority 1: If file_id is provided, try to access it directly
        if file_id and file_id.strip():
            print(f"🔍 Memory Recovery: Direct file_id provided, attempting direct access...")
            try:
                # Get file info using the provided file_id
                file_info_url = f"https://api.telegram.org/bot{bot_token}/getFile"
                file_info_params = {'file_id': file_id.strip()}

                file_info_response = requests.get(file_info_url, params=file_info_params, timeout=10)

                if file_info_response.status_code == 200:
                    file_info_data = file_info_response.json()

                    if file_info_data.get('ok'):
                        file_path = file_info_data['result']['file_path']
                        file_size = file_info_data['result'].get('file_size', 0)

                        # Extract filename from file_path
                        filename = file_path.split('/')[-1] if '/' in file_path else f"backup_{datetime.now().strftime('%Y%m%d')}.zip"

                        backup_files.append({
                            'file_id': file_id.strip(),
                            'file_name': filename,
                            'file_size': file_size,
                            'timestamp': datetime.now().timestamp()  # Current time since we can't get original
                        })

                        print(f"✅ Memory Recovery: Direct file_id access successful - {filename} ({file_size} bytes)")
                    else:
                        print(f"⚠️ Memory Recovery: Direct file_id access failed - {file_info_data.get('description', 'Unknown error')}")
                else:
                    print(f"⚠️ Memory Recovery: Direct file_id access failed - HTTP {file_info_response.status_code}")

            except Exception as e:
                print(f"⚠️ Memory Recovery: Direct file_id access error - {str(e)}")

        # Priority 2: If no direct file_id or it failed, search for backup files in chat
        if not backup_files:
            print(f"🔍 Memory Recovery: Searching for backup files in chat...")

        # Try multiple approaches to get messages
        # Approach 1: Get all available updates (no offset)
        print(f"🔍 Memory Recovery: Trying approach 1 - All available updates...")
        updates_url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        updates_params = {
            'limit': 100,  # Get last 100 messages
        }

        updates_response = requests.get(updates_url, params=updates_params, timeout=10)

        if updates_response.status_code == 200:
            updates_data = updates_response.json()
            print(f"🔍 Memory Recovery: Retrieved {len(updates_data.get('result', []))} messages from approach 1")

            # Look for backup files in the messages
            for update in updates_data.get('result', []):
                message = update.get('message', {})

                # Check if message is from the correct chat
                if str(message.get('chat', {}).get('id', '')) != str(chat_id):
                    continue

                # Check if message has a document attachment
                document = message.get('document')
                if document and document.get('file_name', '').endswith('.zip'):
                    file_name = document.get('file_name', '')
                    if 'memory' in file_name.lower() or 'backup' in file_name.lower():
                        backup_files.append({
                            'file_id': document.get('file_id'),
                            'file_name': file_name,
                            'file_size': document.get('file_size', 0),
                            'timestamp': message.get('date', 0)
                        })

        # Approach 2: If no files found, try with negative offset to get older messages
        if not backup_files:
            print(f"🔍 Memory Recovery: Trying approach 2 - Older messages with negative offset...")
            updates_params = {
                'limit': 100,
                'offset': -100
            }

            updates_response = requests.get(updates_url, params=updates_params, timeout=10)

            if updates_response.status_code == 200:
                updates_data = updates_response.json()
                print(f"🔍 Memory Recovery: Retrieved {len(updates_data.get('result', []))} messages from approach 2")

                for update in updates_data.get('result', []):
                    message = update.get('message', {})

                    if str(message.get('chat', {}).get('id', '')) != str(chat_id):
                        continue

                    document = message.get('document')
                    if document and document.get('file_name', '').endswith('.zip'):
                        file_name = document.get('file_name', '')
                        if 'memory' in file_name.lower() or 'backup' in file_name.lower():
                            backup_files.append({
                                'file_id': document.get('file_id'),
                                'file_name': file_name,
                                'file_size': document.get('file_size', 0),
                                'timestamp': message.get('date', 0)
                            })

        # Approach 3: If still no files, check if we have stored file_ids from the SelfHealing system
        if not backup_files:
            print(f"🔍 Memory Recovery: Trying approach 3 - Check stored file_ids...")
            try:
                import os
                import json
                from app.self_healing_memory import self_healing_memory

                file_id_storage = os.path.join(self_healing_memory.backup_dir, "telegram_file_ids.json")
                print(f"🔍 Memory Recovery: Looking for file_ids at: {file_id_storage}")

                if os.path.exists(file_id_storage):
                    with open(file_id_storage, 'r') as f:
                        file_ids = json.load(f)

                    print(f"🔍 Memory Recovery: Found stored file_ids: {list(file_ids.keys())}")

                    for filename, file_info in file_ids.items():
                        if filename.endswith('.zip') and ('memory' in filename.lower() or 'backup' in filename.lower()):
                            backup_files.append({
                                'file_id': file_info.get('file_id'),
                                'file_name': filename,
                                'file_size': 0,  # Will get from Telegram
                                'timestamp': datetime.fromisoformat(file_info.get('timestamp', datetime.now().isoformat())).timestamp()
                            })

            except Exception as e:
                print(f"⚠️ Memory Recovery: Could not check stored file_ids: {str(e)}")

        # Approach 4: PRODUCTION-READY - Deep search with multiple strategies (for fresh installs)
        if not backup_files:
            print(f"🔍 Memory Recovery: Trying approach 4 - Deep search strategies (fresh install mode)...")
            try:
                # Strategy 4a: Try getUpdates with very large offset range to catch older messages
                print(f"🔍 Memory Recovery: Strategy 4a - Large offset range search...")
                for offset_start in [-1000, -500, -200, 0, 200, 500]:  # Search across wide range
                    updates_params = {
                        'limit': 100,
                        'offset': offset_start
                    }

                    updates_response = requests.get(updates_url, params=updates_params, timeout=10)

                    if updates_response.status_code == 200:
                        updates_data = updates_response.json()
                        messages_found = len(updates_data.get('result', []))
                        print(f"🔍 Memory Recovery: Offset {offset_start}: {messages_found} messages")

                        for update in updates_data.get('result', []):
                            message = update.get('message', {})

                            if str(message.get('chat', {}).get('id', '')) != str(chat_id):
                                continue

                            document = message.get('document')
                            if document and document.get('file_name', '').endswith('.zip'):
                                file_name = document.get('file_name', '')
                                if 'memory' in file_name.lower() or 'backup' in file_name.lower():
                                    backup_files.append({
                                        'file_id': document.get('file_id'),
                                        'file_name': file_name,
                                        'file_size': document.get('file_size', 0),
                                        'timestamp': message.get('date', 0)
                                    })
                                    print(f"✅ Memory Recovery: Found backup in deep search: {file_name}")

                        # If we found files, no need to search further
                        if backup_files:
                            break

                    # Small delay between requests to avoid rate limiting
                    import time
                    time.sleep(0.1)

            except Exception as e:
                print(f"⚠️ Memory Recovery: Deep search failed: {str(e)}")

        # Approach 5: ULTIMATE FALLBACK - Return special flag for manual mode
        if not backup_files:
            print(f"🔍 Memory Recovery: No automatic detection possible - enabling manual recovery mode...")
            # Return a special response indicating manual mode is needed
            return {
                "found": False,
                "backupCount": 0,
                "latestBackup": None,
                "manualModeRequired": True,
                "message": "No backups found automatically. Please use manual recovery with file_id from your Telegram chat."
            }

        print(f"🔍 Memory Recovery: Found {len(backup_files)} backup files")

        if not backup_files:
            print(f"❌ Memory Recovery: No backup files found in chat")
            return {
                "found": False,
                "backupCount": 0,
                "latestBackup": None
            }

        # Get the most recent backup file
        latest_backup = max(backup_files, key=lambda x: x['timestamp'])

        # Convert timestamp to readable format
        timestamp_readable = datetime.fromtimestamp(latest_backup['timestamp']).isoformat()

        print(f"✅ Memory Recovery: Latest backup found - {latest_backup['file_name']} from {timestamp_readable}")

        # Try to get actual record count from the backup file
        record_count = "Unknown"
        try:
            record_count = await get_backup_record_count(bot_token, latest_backup['file_id'])
            print(f"✅ Memory Recovery: Record count retrieved - {record_count}")
        except Exception as e:
            print(f"⚠️ Memory Recovery: Could not get record count - {str(e)}")

        return {
            "found": True,
            "backupCount": len(backup_files),
            "latestBackup": {
                "timestamp": timestamp_readable,
                "size": f"{latest_backup['file_size'] / 1024 / 1024:.2f} MB" if latest_backup['file_size'] > 0 else "Unknown",
                "records": record_count,
                "file_name": latest_backup['file_name'],
                "file_id": latest_backup['file_id']  # Store for restore
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Memory Recovery: Search failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.post("/api/self-healing/restore-from-telegram")
async def restore_from_telegram(request: dict):
    """🔄 Restore memory from Telegram backup (for Memory Recovery Wizard)"""
    try:
        from app.self_healing_memory import self_healing_memory, RecoveryLevel

        bot_token = request.get('botToken')
        chat_id = request.get('chatId')

        if not bot_token or not chat_id:
            raise HTTPException(status_code=400, detail="Bot token and chat ID are required")

        # Validate credentials format
        import re
        if not re.match(r'^\d+:[A-Za-z0-9_-]+$', bot_token):
            raise HTTPException(status_code=400, detail="Invalid bot token format")

        if not re.match(r'^-?\d+$', chat_id):
            raise HTTPException(status_code=400, detail="Invalid chat ID format")

        # Temporarily update credentials for restore
        original_token = self_healing_memory.telegram_bot_token
        original_chat_id = self_healing_memory.telegram_chat_id
        original_enabled = self_healing_memory.telegram_enabled

        try:
            self_healing_memory.telegram_bot_token = bot_token
            self_healing_memory.telegram_chat_id = chat_id
            self_healing_memory.telegram_enabled = True

            # Use the existing startup restore functionality
            await self_healing_memory._startup_restore_from_telegram()

            # Check if restore was successful by checking memory count
            from app.postgresql_memory import PostgresMemory
            memory = PostgresMemory()

            restored_records = 0
            if memory.enabled:
                recent_memories = memory.retrieve_memory(limit=10000)
                restored_records = len(recent_memories) if recent_memories else 0

            if restored_records > 0:
                return {
                    "success": True,
                    "message": "Memory restoration completed successfully! ARIA is now operational with restored data.",
                    "restoredRecords": restored_records
                }
            else:
                return {
                    "success": False,
                    "message": "No memories found after restore attempt",
                    "error": "Restore completed but no memory records were found"
                }

        finally:
            # Restore original credentials
            self_healing_memory.telegram_bot_token = original_token
            self_healing_memory.telegram_chat_id = original_chat_id
            self_healing_memory.telegram_enabled = original_enabled

    except Exception as e:
        return {
            "success": False,
            "message": "Memory restoration failed",
            "error": str(e)
        }

@app.post("/api/self-healing/manual-recovery")
async def manual_memory_recovery(request: Request):
    """🔧 Manual memory recovery for fresh installations using file_id"""
    try:
        data = await request.json()
        file_id = data.get('file_id', '').strip()

        if not file_id:
            return {
                "success": False,
                "message": "File ID is required for manual recovery"
            }

        print(f"🔧 Manual Recovery: Attempting recovery with file_id: {file_id[:20]}...")

        # Get Telegram credentials
        from app.config import config
        bot_token = config.get('telegram', {}).get('bot_token')

        if not bot_token:
            return {
                "success": False,
                "message": "Telegram bot token not configured"
            }

        # Download the file using the provided file_id
        file_info_url = f"https://api.telegram.org/bot{bot_token}/getFile"
        file_info_params = {'file_id': file_id}

        file_info_response = requests.get(file_info_url, params=file_info_params, timeout=10)

        if file_info_response.status_code != 200:
            return {
                "success": False,
                "message": f"Failed to get file info from Telegram: {file_info_response.status_code}"
            }

        file_info_data = file_info_response.json()

        if not file_info_data.get('ok'):
            return {
                "success": False,
                "message": f"Telegram API error: {file_info_data.get('description', 'Unknown error')}"
            }

        file_path = file_info_data['result']['file_path']
        file_size = file_info_data['result'].get('file_size', 0)

        # Download the actual file
        download_url = f"https://api.telegram.org/file/bot{bot_token}/{file_path}"
        download_response = requests.get(download_url, timeout=30)

        if download_response.status_code != 200:
            return {
                "success": False,
                "message": f"Failed to download backup file: {download_response.status_code}"
            }

        # Save the downloaded file
        import os
        from app.self_healing_memory import self_healing_memory

        backup_filename = f"manual_recovery_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        backup_path = os.path.join(self_healing_memory.backup_dir, backup_filename)

        with open(backup_path, 'wb') as f:
            f.write(download_response.content)

        print(f"✅ Manual Recovery: Downloaded backup file: {backup_filename} ({file_size} bytes)")

        # Restore the memory from the downloaded backup
        restore_success = await self_healing_memory._restore_from_backup(backup_path)

        if restore_success:
            # Store the file_id for future use
            self_healing_memory._store_file_id(backup_filename, file_id)

            return {
                "success": True,
                "message": f"Memory successfully restored from manual backup ({file_size} bytes)",
                "filename": backup_filename,
                "fileSize": file_size
            }
        else:
            return {
                "success": False,
                "message": "Failed to restore memory from backup file"
            }

    except Exception as e:
        print(f"❌ Manual Recovery: Error - {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "message": f"Manual recovery failed: {str(e)}"
        }

@app.get("/api/self-healing/file-ids")
async def get_stored_file_ids():
    """📂 Get all stored file IDs for user reference (for manual recovery)"""
    try:
        from app.self_healing_memory import self_healing_memory
        import os
        import json

        file_id_storage = os.path.join(self_healing_memory.backup_dir, "telegram_file_ids.json")

        if not os.path.exists(file_id_storage):
            return {
                "success": True,
                "fileIds": [],
                "message": "No stored file IDs found"
            }

        with open(file_id_storage, 'r') as f:
            file_ids = json.load(f)

        # Format for user display
        formatted_file_ids = []
        for filename, file_info in file_ids.items():
            formatted_file_ids.append({
                "filename": filename,
                "fileId": file_info.get('file_id'),
                "timestamp": file_info.get('timestamp'),
                "uploadedBy": file_info.get('uploaded_by', 'unknown')
            })

        # Sort by timestamp (newest first)
        formatted_file_ids.sort(key=lambda x: x['timestamp'], reverse=True)

        return {
            "success": True,
            "fileIds": formatted_file_ids,
            "message": f"Found {len(formatted_file_ids)} stored file IDs"
        }

    except Exception as e:
        print(f"❌ Error getting stored file IDs: {str(e)}")
        return {
            "success": False,
            "fileIds": [],
            "message": f"Error retrieving file IDs: {str(e)}"
        }

# =============================================================================
# Settings Management API
# =============================================================================

class SettingRequest(BaseModel):
    category: str
    key: str
    value: Any
    encrypt: bool = False

class CategoryRequest(BaseModel):
    category: str
    data: Dict[str, Any]

class MT5AccountRequest(BaseModel):
    nickname: str
    login: str
    password: str
    server: str
    account_type: str = "demo"
    brokerId: Optional[str] = None
    brokerName: Optional[str] = None

class TestConnectionRequest(BaseModel):
    login: str
    password: str
    server: str

@app.get("/api/settings/{category}")
async def get_settings_category(category: str):
    """📋 Get settings category"""
    try:
        data = settings_manager.get_category(category)

        # Don't return encrypted passwords
        if category == "mt5_accounts":
            for account_id, account in data.items():
                if "password" in account:
                    account["password"] = "***encrypted***"

        return {"success": True, "data": data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/settings/set")
async def set_setting(request: SettingRequest):
    """✏️ Set individual setting"""
    try:
        success = settings_manager.set_setting(
            request.category,
            request.key,
            request.value,
            request.encrypt
        )

        if success:
            return {"success": True, "message": "Setting saved successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to save setting")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ai-write-system")
async def ai_write_system_setting(request: Request):
    """🤖 AI Assistant system write capability with confirmation"""
    try:
        data = await request.json()
        category = data.get('category')
        key = data.get('key')
        value = data.get('value')
        description = data.get('description', '')
        confirm = data.get('confirm', False)

        if not all([category, key, value is not None]):
            raise HTTPException(status_code=400, detail="Missing required fields: category, key, value")

        # Get current value for confirmation
        current_value = settings_manager.get_setting(category, key, "Not Set")

        if not confirm:
            # Return confirmation request
            return {
                "success": False,
                "requires_confirmation": True,
                "message": f"AI wants to change {category}.{key} from '{current_value}' to '{value}'. Confirm?",
                "current_value": current_value,
                "new_value": value,
                "category": category,
                "key": key,
                "description": description
            }

        # Execute the write
        success = settings_manager.set_setting(category, key, value)

        if success:
            return {
                "success": True,
                "message": f"AI successfully updated {category}.{key}",
                "old_value": current_value,
                "new_value": value
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to save setting")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/hot-reload")
async def trigger_hot_reload(request: Request):
    """🔥 Trigger hot reload of system configuration"""
    try:
        data = await request.json() if request.headers.get("content-type") == "application/json" else {}
        config_type = data.get('config_type', None)

        from app.hot_reload_integrations import create_hot_reload_api_endpoint
        hot_reload_func = create_hot_reload_api_endpoint()
        result = hot_reload_func(config_type)

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/hot-reload/status")
async def get_hot_reload_status():
    """🔥 Get hot reload system status"""
    try:
        from app.hot_reload_manager import hot_reload_manager

        return {
            "success": True,
            "monitoring": hot_reload_manager.monitoring,
            "last_reload": hot_reload_manager.last_settings_mtime,
            "registered_callbacks": len(hot_reload_manager.callbacks),
            "callback_types": list(hot_reload_manager.callbacks.keys())
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/settings/category")
async def set_settings_category(request: CategoryRequest):
    """📝 Set entire settings category"""
    try:
        success = settings_manager.set_category(request.category, request.data)

        if success:
            return {"success": True, "message": "Category saved successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to save category")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/settings/test/telegram")
async def test_telegram_connection():
    """📱 Test Telegram connection"""
    try:
        bot_token = settings_manager.get_setting("notifications", "telegram_bot_token", decrypt=True)
        chat_id = settings_manager.get_setting("notifications", "telegram_chat_id")

        if not bot_token or not chat_id:
            return {"success": False, "message": "Telegram credentials not configured"}

        # Send test message
        import requests
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': '🧪 Test message from NexGen Settings Panel'
        }

        response = requests.post(url, data=data, timeout=10)

        if response.status_code == 200:
            return {"success": True, "message": "Telegram connection successful!"}
        else:
            return {"success": False, "message": f"Telegram API error: {response.status_code}"}

    except Exception as e:
        return {"success": False, "message": f"Connection error: {str(e)}"}

@app.post("/api/settings/test/trading-telegram")
async def test_trading_telegram(request: dict):
    """🚀 Test trading Telegram bot"""
    try:
        bot_token = request.get('bot_token')
        chat_id = request.get('chat_id')

        if not bot_token or not chat_id:
            return {"success": False, "message": "Bot token and chat ID are required"}

        import requests
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': '🚀 <b>Trading Bot Test</b>\n\nThis is a test message from your NexGen Trading Notifications Bot!\n\n✅ Trading alerts are working correctly.',
            'parse_mode': 'HTML'
        }

        response = requests.post(url, data=data, timeout=10)

        if response.status_code == 200:
            return {"success": True, "message": "Trading bot test successful!"}
        else:
            return {"success": False, "message": f"Failed to send message: {response.status_code}"}

    except Exception as e:
        return {"success": False, "message": f"Error: {str(e)}"}

@app.post("/api/settings/test/memory-telegram")
async def test_memory_telegram(request: dict):
    """🛡️ Test memory/system Telegram bot"""
    try:
        bot_token = request.get('bot_token')
        chat_id = request.get('chat_id')

        if not bot_token or not chat_id:
            return {"success": False, "message": "Bot token and chat ID are required"}

        import requests
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': '🛡️ <b>Memory/System Bot Test</b>\n\nThis is a test message from your NexGen Memory & System Notifications Bot!\n\n✅ System alerts are working correctly.',
            'parse_mode': 'HTML'
        }

        response = requests.post(url, data=data, timeout=10)

        if response.status_code == 200:
            return {"success": True, "message": "Memory bot test successful!"}
        else:
            return {"success": False, "message": f"Failed to send message: {response.status_code}"}

    except Exception as e:
        return {"success": False, "message": f"Error: {str(e)}"}


@app.post("/api/settings/test/email")
async def test_email_configuration(request: dict):
    """📧 Test email configuration"""
    try:
        provider = request.get('provider', 'smtp')
        email_address = request.get('email_address')

        if not email_address:
            return {"success": False, "message": "Email address is required"}

        # Import email service
        from app.email_service import EmailNotificationService

        # Create temporary email service with provided config
        if provider == 'smtp':
            # Validate SMTP settings
            smtp_server = request.get('smtp_server')
            smtp_port = request.get('smtp_port', 587)
            username = request.get('username')
            password = request.get('password')

            if not all([smtp_server, username, password]):
                return {"success": False, "message": "SMTP configuration incomplete"}

            # Test SMTP connection
            import smtplib
            from email.mime.text import MIMEText

            msg = MIMEText("🚀 Test email from NexGen TraderPro!\n\nYour email configuration is working correctly.")
            msg['Subject'] = "[Test] NexGen TraderPro Email Configuration"
            msg['From'] = email_address
            msg['To'] = email_address

            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(username, password)
                server.send_message(msg)

            return {"success": True, "message": "SMTP email sent successfully! Check your inbox."}

        elif provider == 'mailgun':
            # Validate Mailgun settings
            domain = request.get('mailgun_domain')
            api_key = request.get('mailgun_api_key')

            if not all([domain, api_key]):
                return {"success": False, "message": "Mailgun configuration incomplete"}

            # Test Mailgun API
            import requests

            response = requests.post(
                f"https://api.mailgun.net/v3/{domain}/messages",
                auth=('api', api_key),
                data={
                    'from': f"NexGen TraderPro <{email_address}>",
                    'to': email_address,
                    'subject': "[Test] NexGen TraderPro Email Configuration",
                    'text': "🚀 Test email from NexGen TraderPro!\n\nYour Mailgun configuration is working correctly."
                },
                timeout=30
            )

            if response.status_code == 200:
                return {"success": True, "message": "Mailgun email sent successfully! Check your inbox."}
            else:
                return {"success": False, "message": f"Mailgun API error: {response.status_code} - {response.text}"}

        else:
            return {"success": False, "message": "Invalid email provider"}

    except Exception as e:
        return {"success": False, "message": f"Email test failed: {str(e)}"}

@app.get("/api/settings/notification-services/status")
async def get_notification_services_status():
    """📡 Get status of all notification services"""

    try:
        services = []

        # Check Telegram services
        trading_token = settings_manager.get_setting("notifications", "trading_telegram_bot_token", decrypt=True)
        trading_chat = settings_manager.get_setting("notifications", "trading_telegram_chat_id")

        if trading_token or settings_manager.get_setting("notifications", "telegram_bot_token", decrypt=True):
            services.append({
                "id": "telegram_trading",
                "type": "telegram",
                "name": "Trading Notifications Bot",
                "status": "active" if (trading_token and trading_chat) else "inactive",
                "info": trading_chat or "Not configured",
                "icon": "trending-up",
                "color": "green"
            })

        try:
            memory_token = settings_manager.get_setting("notifications", "memory_telegram_bot_token")
            memory_chat = settings_manager.get_setting("notifications", "memory_telegram_chat_id")


        except Exception as e:
            memory_token = None
            memory_chat = None

        if memory_token:
            services.append({
                "id": "telegram_memory",
                "type": "telegram",
                "name": "Memory/System Bot",
                "status": "active" if (memory_token and memory_chat) else "inactive",
                "info": memory_chat or "Not configured",
                "icon": "shield",
                "color": "purple"
            })

        # Check Email services
        email_recipients = settings_manager.get_setting("notifications", "email_recipients", [])
        main_email = settings_manager.get_setting("notifications", "email_address")
        email_enabled = settings_manager.get_setting("notifications", "email_notifications", False)

        all_emails = email_recipients.copy()
        if main_email and main_email not in all_emails:
            all_emails.append(main_email)

        for i, email in enumerate(all_emails):
            if email:
                services.append({
                    "id": f"email_{i}",
                    "type": "email",
                    "name": "Email Notifications",
                    "status": "active" if email_enabled else "inactive",
                    "info": email,
                    "icon": "mail",
                    "color": "blue"
                })

        # Check Email provider availability
        providers = []

        # SMTP Provider
        import os
        smtp_configured = bool(
            os.getenv("SMTP_SERVER") or
            settings_manager.get_setting("notifications", "email_smtp_server")
        )
        providers.append({
            "name": "SMTP Provider",
            "status": "configured" if smtp_configured else "available",
            "type": "smtp",
            "color": "green" if smtp_configured else "yellow",
            "info": "Environment configured" if os.getenv("SMTP_SERVER") else "Settings configured" if smtp_configured else "Not configured"
        })

        # Mailgun Provider
        mailgun_configured = bool(
            os.getenv("MAILGUN_API_KEY") or
            settings_manager.get_setting("notifications", "mailgun_api_key")
        )
        providers.append({
            "name": "Mailgun API",
            "status": "configured" if mailgun_configured else "available",
            "type": "mailgun",
            "color": "blue" if mailgun_configured else "yellow",
            "info": "Environment configured" if os.getenv("MAILGUN_API_KEY") else "Settings configured" if mailgun_configured else "Not configured"
        })

        return {
            "success": True,
            "services": services,
            "providers": providers
        }

    except Exception as e:
        return {"success": False, "message": f"Error getting service status: {str(e)}"}

@app.post("/api/notifications/reload")
async def reload_notification_config():
    """🔄 Reload notification service configuration"""
    try:
        from app.trading_notification_service import trading_notification_service
        trading_notification_service.reload_config()

        # Also reload memory backup telegram configuration
        try:
            from app.self_healing_memory import self_healing_memory
            self_healing_memory.reload_telegram_config()
        except Exception as memory_error:
            print(f"⚠️ Warning: Could not reload memory backup config: {memory_error}")

        return {"success": True, "message": "Notification configuration reloaded successfully"}
    except Exception as e:
        return {"success": False, "message": f"Error reloading notification config: {str(e)}"}

# =============================================================================
# Memory Recovery Telegram Credentials API
# =============================================================================

@app.post("/api/settings/telegram-credentials")
async def save_telegram_credentials_for_memory_recovery(request: dict):
    """💾 Save Telegram credentials for memory recovery and automatic backups"""
    try:
        bot_token = request.get('botToken')
        chat_id = request.get('chatId')
        auto_backup_enabled = request.get('autoBackupEnabled', True)

        if not bot_token or not chat_id:
            raise HTTPException(status_code=400, detail="Bot token and chat ID are required")

        # Validate credentials format
        import re
        if not re.match(r'^\d+:[A-Za-z0-9_-]+$', bot_token):
            raise HTTPException(status_code=400, detail="Invalid bot token format")

        if not re.match(r'^-?\d+$', chat_id):
            raise HTTPException(status_code=400, detail="Invalid chat ID format")

        # Save to settings
        settings_manager.update_setting("notifications", "memory_telegram_bot_token", bot_token)
        settings_manager.update_setting("notifications", "memory_telegram_chat_id", chat_id)
        settings_manager.update_setting("notifications", "memory_backup_enabled", auto_backup_enabled)

        # Reload self-healing system configuration
        try:
            from app.self_healing_memory import self_healing_memory
            self_healing_memory.reload_telegram_config()
        except Exception as reload_error:
            print(f"⚠️ Warning: Could not reload self-healing memory config: {reload_error}")

        return {"success": True, "message": "Telegram credentials saved successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save credentials: {str(e)}")

@app.delete("/api/settings/telegram-credentials")
async def clear_telegram_credentials_for_memory_recovery():
    """🗑️ Clear Telegram credentials for memory recovery"""
    try:
        # Clear from settings
        settings_manager.update_setting("notifications", "memory_telegram_bot_token", "")
        settings_manager.update_setting("notifications", "memory_telegram_chat_id", "")
        settings_manager.update_setting("notifications", "memory_backup_enabled", False)

        # Reload self-healing system configuration
        try:
            from app.self_healing_memory import self_healing_memory
            self_healing_memory.reload_telegram_config()
        except Exception as reload_error:
            print(f"⚠️ Warning: Could not reload self-healing memory config: {reload_error}")

        return {"success": True, "message": "Telegram credentials cleared successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear credentials: {str(e)}")

# =============================================================================
# Multi-Account Management API
# =============================================================================

@app.get("/api/accounts")
async def get_mt5_accounts():
    """📋 Get all MT5 accounts"""
    try:
        accounts = multi_account_manager.get_accounts()
        return {"success": True, "accounts": accounts}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/accounts/add")
async def add_mt5_account(request: MT5AccountRequest):
    """➕ Add new MT5 account"""
    try:
        result = multi_account_manager.add_account(
            request.nickname,
            request.login,
            request.password,
            request.server,
            request.account_type,
            broker_id=request.brokerId,
            broker_name=request.brokerName
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["message"])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/accounts/{account_id}")
async def remove_mt5_account(account_id: str):
    """🗑️ Remove MT5 account"""
    try:
        result = multi_account_manager.remove_account(account_id)

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["message"])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/accounts/test")
async def test_mt5_connection(request: TestConnectionRequest):
    """🔍 Test MT5 account connection"""
    try:
        result = multi_account_manager.test_account_connection(
            request.login,
            request.password,
            request.server
        )
        return result
    except Exception as e:
        return {"success": False, "message": f"Test failed: {str(e)}"}

@app.post("/api/accounts/{account_id}/toggle")
async def toggle_mt5_account(account_id: str, enabled: bool):
    """🔄 Enable/disable MT5 account"""
    try:
        success = multi_account_manager.toggle_account(account_id, enabled)

        if success:
            return {"success": True, "message": f"Account {'enabled' if enabled else 'disabled'}"}
        else:
            raise HTTPException(status_code=400, detail="Account not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/accounts/{account_id}/connect")
async def connect_mt5_account(account_id: str):
    """🔗 Connect to MT5 account"""
    try:
        result = multi_account_manager.connect_account(account_id)
        return result
    except Exception as e:
        return {"success": False, "message": f"Connection failed: {str(e)}"}

@app.post("/api/accounts/refresh-active")
async def refresh_active_account_balance():
    """🔄 Refresh balance for currently active account"""
    try:
        result = multi_account_manager.refresh_active_account_balance()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/accounts/{account_id}/switch")
async def switch_to_account(account_id: str):
    """🔄 Switch to a specific MT5 account"""
    try:
        result = multi_account_manager.switch_to_account(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/accounts/active")
async def get_active_account():
    """📍 Get currently active account"""
    try:
        active_account = multi_account_manager.get_active_account()
        if active_account:
            return {"success": True, "active_account": active_account}
        else:
            return {"success": False, "message": "No active account"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/accounts/{account_id}/live-data")
async def get_account_live_data(account_id: str):
    """📊 Get live account data from MT5"""
    try:
        result = multi_account_manager.get_account_live_data(account_id)
        return result
    except Exception as e:
        return {"success": False, "message": f"Error getting live data: {str(e)}"}

@app.get("/api/dual-terminals/status")
async def get_dual_terminals_status():
    """📊 Get status of both MT5 terminals (Process-Based)"""
    try:
        from app.dual_terminal_process_manager import dual_terminal_manager
        status = dual_terminal_manager.get_status()
        return {"success": True, "terminals": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/dual-terminals/initialize")
async def initialize_dual_terminals():
    """🚀 Initialize both MT5 terminals (Process-Based)"""
    try:
        from app.dual_terminal_process_manager import dual_terminal_manager
        results = dual_terminal_manager.initialize_terminals()
        return {"success": True, "results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/dual-terminals/shutdown")
async def shutdown_dual_terminals():
    """🔌 Shutdown both MT5 terminals (Process-Based)"""
    try:
        from app.dual_terminal_process_manager import dual_terminal_manager
        dual_terminal_manager.stop_terminal_processes()
        return {"success": True, "message": "Both terminal processes stopped"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/account-info/{account_type}")
async def get_account_info_by_type(account_type: str):
    """📊 Get account info for specific terminal type (demo/live) - Process-Based"""
    try:
        from app.dual_terminal_process_manager import dual_terminal_manager
        if account_type not in ['demo', 'live']:
            raise HTTPException(status_code=400, detail="Account type must be 'demo' or 'live'")

        account_info = dual_terminal_manager.get_account_info(account_type)
        if account_info:
            return {"success": True, "account_info": account_info}
        else:
            return {"success": False, "message": f"{account_type} terminal not connected"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/execute-trade/{account_type}")
async def execute_trade_on_terminal(account_type: str, trade_data: dict):
    """📈 Execute trade on specific terminal type - Process-Based"""
    try:
        from app.dual_terminal_process_manager import dual_terminal_manager
        if account_type not in ['demo', 'live']:
            raise HTTPException(status_code=400, detail="Account type must be 'demo' or 'live'")

        # For now, return success but note that trade execution needs to be implemented in terminal processes
        return {
            "success": True,
            "message": f"Trade routing to {account_type} terminal",
            "note": "Trade execution implementation needed in terminal processes"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/accounts/{account_id}/assign-terminal")
async def assign_account_to_terminal(account_id: str, assignment_data: dict):
    """🎯 Assign account to specific dual terminal"""
    try:
        terminal_type = assignment_data.get('terminal_type')
        if terminal_type not in ['demo', 'live']:
            raise HTTPException(status_code=400, detail="Terminal type must be 'demo' or 'live'")

        # Update account settings with terminal assignment
        result = multi_account_manager.assign_to_dual_terminal(account_id, terminal_type)

        if result['success']:
            return {"success": True, "message": f"Account assigned to {terminal_type} terminal"}
        else:
            return {"success": False, "message": result.get('message', 'Assignment failed')}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/dual-terminals/assignments")
async def get_dual_terminal_assignments():
    """📋 Get current dual terminal assignments"""
    try:
        assignments = multi_account_manager.get_dual_terminal_assignments()
        return {"success": True, "assignments": assignments}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/intelligent-trade")
async def execute_intelligent_trade(trade_data: dict):
    """🧠 Execute trade with intelligent demo/live routing"""
    try:
        from app.intelligent_trading_router import intelligent_router

        result = intelligent_router.execute_intelligent_trade(trade_data)
        return {"success": True, "trade_result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/intelligent-trading/stats")
async def get_intelligent_trading_stats():
    """📊 Get intelligent trading routing statistics"""
    try:
        from app.intelligent_trading_router import intelligent_router

        stats = intelligent_router.get_routing_stats()
        return {"success": True, "stats": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/intelligent-trading/adjust-threshold")
async def adjust_confidence_threshold(threshold_data: dict):
    """🎯 Adjust confidence threshold for live trading"""
    try:
        from app.intelligent_trading_router import intelligent_router

        new_threshold = threshold_data.get('threshold', 0.85)
        intelligent_router.adjust_confidence_threshold(new_threshold)

        return {"success": True, "new_threshold": intelligent_router.confidence_threshold_live}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/intelligent-trading/recommendation")
async def get_trading_recommendation(symbol: str = "EURUSD"):
    """🎯 Get AI trading recommendation with routing"""
    try:
        from app.intelligent_trading_router import intelligent_router

        # Mock market analysis - replace with your actual AI analysis
        market_analysis = {
            'confidence': 0.75,
            'signal_strength': 0.8,
            'market_conditions': {
                'volatility': 0.3,
                'high_impact_news': False,
                'trend_strength': 0.7
            }
        }

        recommendation = intelligent_router.get_recommended_action(market_analysis)
        return {"success": True, "recommendation": recommendation, "symbol": symbol}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/trading/sessions/status")
async def get_professional_trading_sessions_status():
    """🕐 Get professional trading sessions status"""
    try:
        status = professional_sessions.get_session_status()
        return {"success": True, "data": status}
    except Exception as e:
        print(f"❌ Error getting trading sessions status: {e}")
        return {"success": False, "error": str(e)}

# 📸 TRADING SCREENSHOT ENDPOINTS

@app.post("/api/telegram/test")
async def test_telegram_connection():
    """🧪 Test existing trading bot connection for screenshots"""
    try:
        from app.personal_telegram_notifier import personal_telegram
        result = await personal_telegram.test_connection()

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/telegram/status")
async def get_telegram_status():
    """📊 Get Telegram bot status"""
    try:
        from app.personal_telegram_notifier import personal_telegram
        status = personal_telegram.get_status()

        return {"success": True, "status": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))




@app.get("/api/trading/sessions/should-trade")
async def should_trade_now():
    """🎯 Check if trading should be active now"""
    try:
        should_trade, reason, recommended_pairs = professional_sessions.should_trade_now()
        return {
            "success": True,
            "data": {
                "should_trade": should_trade,
                "reason": reason,
                "recommended_pairs": recommended_pairs,
                "volatility_multiplier": professional_sessions.get_session_volatility_multiplier()
            }
        }
    except Exception as e:
        print(f"❌ Error checking trading status: {e}")
        return {"success": False, "error": str(e)}