import uvicorn
from threading import Thread
import time
import schedule
import signal
import sys
import MetaTrader5 as mt5
import pandas as pd
import argparse

from app.main import initialize_components, trade_cycle, reset_daily_protocols, anomaly_detection_cycle, auto_tuning_cycle, position_management_cycle, log_ai_decision
from app.api import app
from app.config import config
# Backtesting removed - will be implemented separately
from app.mt5_manager import mt5_manager
from app.trading_notification_service import trading_notification_service
from app.master_scheduler_coordinator import master_scheduler, TaskPriority
from datetime import datetime, timedelta

# --- Signal Handler for Clean Shutdown ---
def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    print(f"\n🛑 Received signal {signum}, initiating clean shutdown...")

    # Mark clean shutdown
    try:
        from app.self_healing_memory import self_healing_memory
        self_healing_memory._mark_clean_shutdown()
        print("✅ Clean shutdown flag created")
    except Exception as e:
        print(f"⚠️ Error marking clean shutdown: {e}")

    # Shutdown MT5 connections
    try:
        mt5_manager.shutdown()
        print("✅ MT5 connections closed")
    except Exception as e:
        print(f"⚠️ Error shutting down MT5: {e}")

    print("🏁 Clean shutdown completed")
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

# --- Global State ---
# This dictionary holds the shared state and components for the application.
app_state = {
    "trading_enabled": True,
    "scheduler_thread": None,
    "components": {}
}

# Global scheduler statistics
scheduler_stats = {
    'jobs_executed': 0,
    'jobs_failed': 0,
    'last_execution': None,
    'job_history': [],
    'error_history': []
}

def safe_job_wrapper(job_name, job_function, *args, **kwargs):
    """Wrapper to safely execute scheduled jobs with error handling and monitoring"""
    global scheduler_stats

    try:
        print(f"[SCHEDULER] Starting job: {job_name}", flush=True)
        start_time = datetime.now()

        result = job_function(*args, **kwargs)

        duration = (datetime.now() - start_time).total_seconds()
        print(f"[SCHEDULER] ✅ Job completed: {job_name} (took {duration:.2f}s)", flush=True)

        # Update statistics
        scheduler_stats['jobs_executed'] += 1
        scheduler_stats['last_execution'] = start_time
        scheduler_stats['job_history'].append({
            'job_name': job_name,
            'start_time': start_time,
            'duration': duration,
            'status': 'success'
        })

        # Keep only last 100 job records
        if len(scheduler_stats['job_history']) > 100:
            scheduler_stats['job_history'] = scheduler_stats['job_history'][-100:]

        return result

    except Exception as e:
        duration = (datetime.now() - start_time).total_seconds()
        print(f"[SCHEDULER] ❌ Job failed: {job_name} - Error: {e}", flush=True)
        import traceback
        print(f"[SCHEDULER] Traceback for {job_name}: {traceback.format_exc()}", flush=True)

        # Update error statistics
        scheduler_stats['jobs_failed'] += 1
        scheduler_stats['error_history'].append({
            'job_name': job_name,
            'error': str(e),
            'timestamp': datetime.now(),
            'duration': duration
        })

        # Keep only last 50 error records
        if len(scheduler_stats['error_history']) > 50:
            scheduler_stats['error_history'] = scheduler_stats['error_history'][-50:]

        return None

def get_shared_state():
    """Safely get shared state from either app_state or FastAPI app.state"""
    # Try FastAPI app state first (this has the API-controlled trading_enabled flag)
    try:
        from app.api import app
        if hasattr(app.state, 'shared_state') and app.state.shared_state:
            fastapi_state = app.state.shared_state
            # Sync trading_enabled from FastAPI to app_state
            if app_state and "trading_enabled" in fastapi_state:
                app_state["trading_enabled"] = fastapi_state["trading_enabled"]
            return fastapi_state
    except Exception as e:
        print(f"[SHARED_STATE] Could not access FastAPI state: {e}")

    # Fallback to app_state (run.py global)
    if app_state and "components" in app_state:
        return app_state

    return None

def get_scheduler_health():
    """Get scheduler health status and statistics"""
    global scheduler_stats

    total_jobs = scheduler_stats['jobs_executed'] + scheduler_stats['jobs_failed']
    success_rate = (scheduler_stats['jobs_executed'] / total_jobs * 100) if total_jobs > 0 else 0

    recent_errors = [err for err in scheduler_stats['error_history']
                    if (datetime.now() - err['timestamp']).total_seconds() < 3600]  # Last hour

    return {
        'total_jobs_executed': scheduler_stats['jobs_executed'],
        'total_jobs_failed': scheduler_stats['jobs_failed'],
        'success_rate': success_rate,
        'last_execution': scheduler_stats['last_execution'].isoformat() if scheduler_stats['last_execution'] else None,
        'recent_errors_count': len(recent_errors),
        'recent_errors': recent_errors[-5:],  # Last 5 errors
        'is_healthy': success_rate > 80 and len(recent_errors) < 10
    }

def enhanced_trade_cycle(components):
    """🚀 ENHANCED TRADING CYCLE with ALL ADVANCED PHASES"""
    print("[TRACE] enhanced_trade_cycle START", flush=True)
    try:
        print("🎯 Running enhanced autonomous trading cycle...")

        # Validate components exist before using them
        required_components = [
            'reasoning_analyzer', 'reasoning_quality_metrics', 'performance_detector',
            'parameter_tuner', 'confidence_calibrator', 'learning_loop',
            'human_ai_collab', 'approval_workflow'
        ]

        missing_components = [comp for comp in required_components if comp not in components]
        if missing_components:
            print(f"⚠️ Missing components for enhanced trade cycle: {missing_components}")
            return

        # 🧠 PHASE 1: LLM Reasoning Analysis
        reasoning_analyzer = components['reasoning_analyzer']
        reasoning_quality = components['reasoning_quality_metrics']

        # 🤖 PHASE 2: Autonomous Learning & Adaptation
        performance_detector = components['performance_detector']
        parameter_tuner = components['parameter_tuner']
        confidence_calibrator = components['confidence_calibrator']
        learning_loop = components['learning_loop']

        # 👥 PHASE 3: Human-AI Collaboration
        human_ai_collab = components['human_ai_collab']
        approval_workflow = components['approval_workflow']

        # 🎯 AUTONOMOUS ORCHESTRATOR
        autonomous_system = components['autonomous_system']

        # Run the autonomous trading system (it orchestrates everything)
        autonomous_system.run_trading_cycle(components)

        # Additional cycles for specific components
        performance_detector.analyze_recent_performance()
        learning_loop.process_feedback()

        print("✅ Enhanced trading cycle completed")
    except Exception as e:
        print(f"❌ Error in enhanced trading cycle: {e}")
        # Fallback to basic trading cycle
        trade_cycle(
            components['feeder'], components['hybrid_brain'],
            components['executor'], components['news_filter'],
            components['anomaly_detector'], components['explain_engine'],
            components['ml_brain'], app_state, components['ultimate_risk_manager']
        )
    print("[TRACE] enhanced_trade_cycle END", flush=True)

def run_scheduler(scheduler):
    """The function that runs in the background thread with enhanced error handling."""
    print("[TRACE] Entered run_scheduler", flush=True)
    consecutive_errors = 0
    max_consecutive_errors = 5

    try:
        print("[TRACE] Jobs registered with scheduler at thread start:", flush=True)
        for job in scheduler.jobs:
            print(f"[TRACE] {job}", flush=True)

        # Run an initial trade cycle immediately with error handling
        try:
            print("[TRACE] Running initial scheduler.run_all()", flush=True)
            scheduler.run_all(delay_seconds=0)
            print("[TRACE] Initial run_all completed successfully", flush=True)
        except Exception as e:
            print(f"[ERROR] Exception in initial run_all: {e}", flush=True)
            consecutive_errors += 1

        # Test forward test function immediately
        print("[TEST] Testing forward test function immediately...", flush=True)
        try:
            auto_forward_test_cycle()
            print("[TEST] ✅ Forward test function executed successfully", flush=True)
        except Exception as e:
            print(f"[TEST] ❌ Forward test function failed: {e}", flush=True)

        print("[TRACE] After run_all, before while loop", flush=True)

        while True:
            try:
                print("[TRACE] run_scheduler loop alive", flush=True)
                print(f"[TRACE] Time: {datetime.now().isoformat()} - About to run_pending()", flush=True)

                # ALWAYS RUN SCHEDULER - No trading_enabled check
                # This ensures forward testing and other scheduled jobs always run
                print("[TRACE] Running all scheduled jobs", flush=True)
                jobs_before = len(scheduler.jobs)
                scheduler.run_pending()
                print(f"[TRACE] run_pending() completed, {jobs_before} jobs checked", flush=True)
                consecutive_errors = 0  # Reset error counter on success

                time.sleep(1)

            except Exception as loop_error:
                consecutive_errors += 1
                print(f"[ERROR] Exception in scheduler loop (#{consecutive_errors}): {loop_error}", flush=True)

                if consecutive_errors >= max_consecutive_errors:
                    print(f"[CRITICAL] Too many consecutive errors ({consecutive_errors}), restarting scheduler loop", flush=True)
                    consecutive_errors = 0
                    time.sleep(5)  # Wait before continuing
                else:
                    time.sleep(2)  # Short wait before retry

    except Exception as e:
        print(f"[CRITICAL] Fatal exception in run_scheduler: {e}", flush=True)
        import traceback
        print(f"[CRITICAL] Traceback: {traceback.format_exc()}", flush=True)
        # Don't exit the thread, try to restart
        time.sleep(10)
        print("[CRITICAL] Attempting to restart scheduler thread...", flush=True)

def main():
    """
    🚀 NEXGEN TRADING BOT - MAIN ENTRY POINT
    Initializes ALL ADVANCED PHASES and starts the autonomous trading system.

    MT5 Background Mode:
    - By default, MT5 terminals run in background without UI
    - Use --show-mt5 flag to show MT5 terminal windows
    - Background mode prevents UI interruptions during automated trading
    """
    print("🚀 Starting NexGen Forex Trading Bot - Advanced AI Edition")
    print("=" * 60)
    print("🧠 Phase 1: LLM Reasoning & Intelligence")
    print("🤖 Phase 2: Autonomous Self-Training")
    print("👥 Phase 3: Human-AI Collaboration")
    print("🎯 Autonomous Trading Orchestrator")
    print("=" * 60)

    # 🚀 AUTO-START BOT: Enable trading automatically on startup
    print("🚀 Auto-starting trading bot...")
    shared_state = get_shared_state()
    if shared_state:
        shared_state["trading_enabled"] = True
        print("✅ Trading bot auto-started - Ready for autonomous operation")
    else:
        print("⚠️ Could not auto-start bot - shared state not available")

    # --- Initialize MT5 Connections ---
    print("🔗 Connecting to MT5 accounts...")

    # Connect to demo account for forward testing and backtesting
    if not mt5_manager.connect_demo():
        print("❌ Demo MT5 initialization failed.")
        return

    # Try to connect to live account (optional, for account stats)
    live_connected = mt5_manager.connect_live()
    if live_connected:
        print("✅ Both Demo and Live MT5 accounts connected!")
    else:
        print("⚠️  Demo account connected. Live account connection failed - Account tab will show demo data.")

    # --- Initialize ALL Advanced Components ---
    print("🔧 Initializing all advanced AI systems...")
    app_state["components"] = initialize_components()
    print("✅ All advanced bot components initialized!")

    # --- Initialize Hot Reload System ---
    print("🔥 Initializing Hot Reload System...")
    try:
        from app.hot_reload_integrations import initialize_hot_reload_integrations
        hot_reload_components = initialize_hot_reload_integrations()
        print("✅ Hot Reload System initialized - All configuration changes will take immediate effect!")
    except Exception as e:
        print(f"⚠️ Hot Reload System initialization failed: {e}")
        print("⚠️ System will continue without hot reload - manual restart required for config changes")

    # --- Initialize Email Scheduler ---
    print("📧 Initializing email notification scheduler...")
    try:
        from app.email_scheduler import email_scheduler
        email_scheduler.start()
        print("✅ Email scheduler started successfully!")
    except Exception as e:
        print(f"⚠️ Email scheduler initialization failed: {e}")

    # --- Share State with FastAPI ---
    # This makes the app_state accessible from within API endpoints
    app.state.shared_state = app_state

    # --- Schedule ADVANCED Trading Tasks ---
    print("📅 Setting up advanced trading schedules...")
    trading_scheduler = schedule.Scheduler()
    components = app_state["components"]

    # 🚀 ENHANCED MAIN TRADING CYCLE with ALL PHASES
    print("🎯 Scheduling autonomous trading cycle...")
    trading_scheduler.every(5).minutes.do(
        safe_job_wrapper, "enhanced_trade_cycle", enhanced_trade_cycle, components
    )

    # 📰 News synchronization - every 1 minute to match EA calendar updates
    if 'news_filter' in components:
        trading_scheduler.every(1).minutes.do(
            safe_job_wrapper, "news_sync_cycle", news_sync_cycle, components['news_filter']
        )
    else:
        print("⚠️ news_filter component not available, skipping news sync scheduling")

    # Daily reset
    trading_scheduler.every().day.at("00:01").do(
        safe_job_wrapper, "reset_daily_protocols", reset_daily_protocols
    )

    # Anomaly detection - every 2 hours (more frequent)
    if 'anomaly_detector' in components:
        trading_scheduler.every(2).hours.do(
            safe_job_wrapper, "anomaly_detection_cycle", anomaly_detection_cycle, components['anomaly_detector']
        )
    else:
        print("⚠️ anomaly_detector component not available, skipping anomaly detection scheduling")

    # 🦢 BLACK SWAN DETECTION - every 5 minutes for rapid response
    trading_scheduler.every(5).minutes.do(
        safe_job_wrapper, "black_swan_detection_cycle", black_swan_detection_cycle
    )

    # 🧠 MEMORY-RAG SYSTEM - Initialize memory index every 2 hours
    trading_scheduler.every(2).hours.do(
        safe_job_wrapper, "memory_rag_maintenance_cycle", memory_rag_maintenance_cycle
    )

    # 🎯 ZONE DETECTION - every 30 minutes for zone updates
    trading_scheduler.every(30).minutes.do(
        safe_job_wrapper, "zone_detection_cycle", zone_detection_cycle
    )

    # 📊 MARKET REGIME DETECTION - every hour for regime analysis
    trading_scheduler.every(1).hours.do(
        safe_job_wrapper, "market_regime_cycle", market_regime_cycle
    )

    # 🛡️ SELF-HEALING MEMORY SYSTEM - automated backup and health monitoring
    trading_scheduler.every(6).hours.do(
        safe_job_wrapper, "self_healing_backup_cycle", self_healing_backup_cycle
    )
    trading_scheduler.every(30).minutes.do(
        safe_job_wrapper, "self_healing_health_check_cycle", self_healing_health_check_cycle
    )

    # 📱 TRADING NOTIFICATIONS - Daily reports at 23:30
    print("📱 Starting trading notification service...")
    trading_notification_service.start_daily_report_scheduler()

    # Auto-tuning - weekly
    if 'auto_tuner' in components:
        trading_scheduler.every().sunday.at("04:00").do(
            safe_job_wrapper, "auto_tuning_cycle", auto_tuning_cycle, components['auto_tuner']
        )
    else:
        print("⚠️ auto_tuner component not available, skipping auto-tuning scheduling")

    # 🧪 ENHANCED TESTING SCHEDULE
    # 📌 Forward Testing / Paper Trading - 24/5 continuous operation
    trading_scheduler.every(5).minutes.do(
        safe_job_wrapper, "auto_forward_test_cycle", auto_forward_test_cycle
    )

    # 📌 Backtesting - every 4-6 hours with AI evaluation
    trading_scheduler.every(4).hours.do(
        safe_job_wrapper, "auto_backtest_cycle", auto_backtest_cycle
    )

    # 🧠 REINFORCEMENT LEARNING TRAINING - Weekly RL model training
    trading_scheduler.every().sunday.at("03:00").do(
        safe_job_wrapper, "weekly_rl_training_cycle", weekly_rl_training_cycle
    )

    # 🔍 WEEKLY COMPREHENSIVE ANALYSIS - Sunday deep dive
    trading_scheduler.every().sunday.at("02:00").do(
        safe_job_wrapper, "weekly_comprehensive_backtest", weekly_comprehensive_backtest
    )

    # 🧠 Position management with AI learning + PYRAMIDING - every 2 minutes
    required_position_components = ['executor', 'hybrid_brain', 'ultimate_risk_manager', 'legendary_executor']
    missing_position_components = [comp for comp in required_position_components if comp not in components]

    if not missing_position_components:
        trading_scheduler.every(2).minutes.do(
            safe_job_wrapper, "position_management_cycle", position_management_cycle,
            components['executor'],
            components['hybrid_brain'],
            components['ultimate_risk_manager'],
            components['legendary_executor']  # 🏆 Enable pyramiding monitoring
        )
    else:
        print(f"⚠️ Missing position management components: {missing_position_components}, using basic position management")
        if 'executor' in components:
            trading_scheduler.every(2).minutes.do(
                safe_job_wrapper, "basic_position_management", position_management_cycle,
                components['executor'], None, None, None
            )

    # 🧹 BACKUP CLEANUP - Daily at 3 AM
    def cleanup_backups():
        """Daily backup cleanup task"""
        try:
            from app.self_healing_memory import self_healing_memory
            print("🧹 Running scheduled backup cleanup...")
            self_healing_memory._cleanup_old_backups()
        except Exception as e:
            print(f"❌ Error in scheduled backup cleanup: {e}")

    trading_scheduler.every().day.at("03:00").do(
        safe_job_wrapper, "cleanup_backups", cleanup_backups
    )

    # 📊 SCHEDULER HEALTH MONITORING - every 15 minutes
    def scheduler_health_check():
        """Monitor scheduler health and log status"""
        try:
            health = get_scheduler_health()
            print(f"[SCHEDULER_HEALTH] 📊 Jobs: {health['total_jobs_executed']} success, "
                  f"{health['total_jobs_failed']} failed, {health['success_rate']:.1f}% success rate")

            if not health['is_healthy']:
                print(f"[SCHEDULER_HEALTH] ⚠️ Scheduler health warning: {health['recent_errors_count']} recent errors")

            # Log to shared state if available
            shared_state = get_shared_state()
            if shared_state:
                log_ai_decision(shared_state,
                    f"Scheduler health: {health['success_rate']:.1f}% success rate, "
                    f"{health['recent_errors_count']} recent errors", "scheduler_health")

        except Exception as e:
            print(f"[SCHEDULER_HEALTH] ❌ Error in health check: {e}")

    trading_scheduler.every(15).minutes.do(
        safe_job_wrapper, "scheduler_health_check", scheduler_health_check
    )

    # 🧠 PHASE 1: LLM Reasoning & Quality Analysis
    print("🧠 Scheduling LLM reasoning analysis...")
    if 'reasoning_analyzer' in components:
        trading_scheduler.every(10).minutes.do(
            safe_job_wrapper, "reasoning_analyzer",
            lambda: components['reasoning_analyzer'].analyze_recent_decisions()
        )
    if 'reasoning_quality_metrics' in components:
        trading_scheduler.every(30).minutes.do(
            safe_job_wrapper, "reasoning_quality_metrics",
            lambda: components['reasoning_quality_metrics'].evaluate_reasoning_quality()
        )

    # 🤖 PHASE 2: Autonomous Learning Cycles
    print("🤖 Scheduling autonomous learning cycles...")
    if 'performance_detector' in components:
        trading_scheduler.every(15).minutes.do(
            safe_job_wrapper, "performance_detector",
            lambda: components['performance_detector'].detect_patterns()
        )
    if 'parameter_tuner' in components:
        trading_scheduler.every(1).hours.do(
            safe_job_wrapper, "parameter_tuner",
            lambda: components['parameter_tuner'].auto_tune_parameters()
        )
    if 'confidence_calibrator' in components:
        trading_scheduler.every(30).minutes.do(
            safe_job_wrapper, "confidence_calibrator",
            lambda: components['confidence_calibrator'].calibrate_confidence()
        )
    if 'learning_loop' in components:
        trading_scheduler.every(20).minutes.do(
            safe_job_wrapper, "learning_loop",
            lambda: components['learning_loop'].process_feedback_cycle()
        )

    # 👥 PHASE 3: Human-AI Collaboration
    print("👥 Scheduling human-AI collaboration...")
    if 'human_ai_collab' in components:
        trading_scheduler.every(5).minutes.do(
            safe_job_wrapper, "human_ai_collab",
            lambda: components['human_ai_collab'].check_collaboration_opportunities()
        )
    if 'approval_workflow' in components:
        trading_scheduler.every(1).minutes.do(
            safe_job_wrapper, "approval_workflow",
            lambda: components['approval_workflow'].process_pending_approvals()
        )
    
    # 🎛️ START MASTER SCHEDULER COORDINATOR
    print("🎛️ Starting Master Scheduler Coordinator...")
    master_scheduler.start_coordinator()

    # Register critical tasks with master coordinator for better coordination
    master_scheduler.register_task(
        "position_management_critical",
        lambda: position_management_cycle(
            components['executor'],
            components['hybrid_brain'],
            components['ultimate_risk_manager'],
            components['legendary_executor']
        ),
        TaskPriority.CRITICAL,
        estimated_duration=45,
        estimated_memory=200.0
    )

    master_scheduler.register_task(
        "forward_testing_coordinated",
        auto_forward_test_cycle,
        TaskPriority.HIGH,
        estimated_duration=180,
        estimated_memory=300.0
    )

    # Schedule critical tasks through coordinator
    master_scheduler.schedule_task("position_management_critical", "minutes", 2)
    master_scheduler.schedule_task("forward_testing_coordinated", "minutes", 5)

    print("✅ Master Scheduler Coordinator configured and running")

    # --- Start Background Scheduler ---
    print("[DEBUG] Scheduled jobs:")
    for job in trading_scheduler.jobs:
        print(f"[DEBUG] {job}")
    print("[DEBUG] About to start scheduler thread", flush=True)
    app_state["scheduler_thread"] = Thread(target=run_scheduler, args=(trading_scheduler,), daemon=True)
    app_state["scheduler_thread"].start()
    print("Trading scheduler started in a background thread.", flush=True)
    print("[DEBUG] Scheduler thread started", flush=True)

    # 🚀 START DEDICATED FORWARD TEST THREAD (BYPASSES SCHEDULER)
    print("[MAIN] 🚀 Starting dedicated forward test thread...", flush=True)
    app_state["forward_test_thread"] = Thread(target=dedicated_forward_test_thread, daemon=True)
    app_state["forward_test_thread"].start()
    print("[MAIN] ✅ Dedicated forward test thread started!", flush=True)
    
    # --- Start FastAPI Server ---
    try:
        uvicorn.run(app, host="127.0.0.1", port=8000)
    finally:
        print("🛑 Shutting down...")

        # Mark clean shutdown
        try:
            from app.self_healing_memory import self_healing_memory
            self_healing_memory._mark_clean_shutdown()
            print("✅ Clean shutdown flag created")
        except Exception as e:
            print(f"⚠️ Error marking clean shutdown: {e}")

        # Shutdown Master Scheduler Coordinator
        master_scheduler.stop_coordinator()
        print("✅ Master Scheduler Coordinator stopped")

        # Shutdown MT5 connections
        mt5_manager.shutdown()
        print("✅ MT5 connections closed")

def news_sync_cycle(news_filter):
    """Synchronize news data every minute to match EA calendar updates"""
    try:
        # Force refresh news cache to get latest calendar.json data
        news_filter.cache = None
        news_filter.expiry = None
        events = news_filter.get_todays_events()

        if events:
            print(f"[NewsSync] ✅ Refreshed {len(events)} calendar events")
        else:
            print("[NewsSync] ⚠️ No calendar events found")

    except Exception as e:
        print(f"[NewsSync] ❌ Error syncing news: {e}")

def auto_forward_test_cycle():
    """📌 24/5 Forward Testing / Paper Trading with prediction logging"""
    print("[TRACE] auto_forward_test_cycle ABSOLUTE TOP", flush=True)
    print("[TRACE] auto_forward_test_cycle TOP")
    print("[DEBUG] auto_forward_test_cycle invoked")
    print(f"[SCHEDULER] 🚀 AUTO FORWARD TEST CYCLE TRIGGERED at {datetime.now()}", flush=True)
    try:
        # Get shared state safely
        shared_state = get_shared_state()
        if not shared_state:
            print("[ForwardTest] No shared state available from any source")
            return

        # Check if trading is enabled
        trading_enabled = shared_state.get("trading_enabled", False)
        if not trading_enabled:
            print("[ForwardTest] 🔴 Trading disabled - skipping forward test")
            return

        # Check if market is open (24/5 operation)
        current_time = datetime.now()
        is_weekend = current_time.weekday() >= 5  # Saturday = 5, Sunday = 6

        print(f"[ForwardTest] 📅 Current time: {current_time}, Weekday: {current_time.weekday()}, Is weekend: {is_weekend}")
        print(f"[ForwardTest] 🔍 Trading enabled: {trading_enabled}")

        if is_weekend:
            print("[ForwardTest] 📅 Weekend - Market closed, skipping forward test")
            return

        print("[ForwardTest] 🚀 Starting 24/5 forward testing cycle...")

        # Import required components
        from app.data_feeder import DataFeeder
        from app.ai_brain import HybridBrain, MLBrain
        from app.postgresql_memory import PostgresMemory

        components = shared_state.get("components", {})
        data_feeder = components.get('data_feeder') or DataFeeder()

        # Get or create HybridBrain with proper MLBrain dependency
        hybrid_brain = components.get('hybrid_brain')
        if not hybrid_brain:
            ml_brain = components.get('ml_brain') or MLBrain()
            hybrid_brain = HybridBrain(ml_brain)

        memory = PostgresMemory()

        forward_test_results = []

        # Run forward test for each symbol
        for symbol in config.SYMBOLS:
            try:
                # Get latest market data
                latest_data = data_feeder.get_features(symbol)
                if latest_data.empty:
                    continue

                # Generate AI prediction using get_decision method
                decision = hybrid_brain.get_decision(symbol, latest_data)
                prediction = decision.action

                # Get GPT/Claude trade rationale
                market_context = {
                    'symbol': symbol,
                    'current_price': latest_data['close'].iloc[-1] if not latest_data.empty else 0,
                    'trend': _analyze_trend(latest_data),
                    'volatility': _calculate_volatility(latest_data),
                    'timestamp': current_time.isoformat()
                }

                rationale_prompt = f"""
                Analyze this trading opportunity and provide detailed rationale:

                Symbol: {symbol}
                Current Price: {market_context['current_price']}
                Market Trend: {market_context['trend']}
                Volatility: {market_context['volatility']}
                AI Prediction: {prediction}

                Provide:
                1. Trade recommendation (BUY/SELL/HOLD)
                2. Confidence level (0-100%)
                3. Risk assessment
                4. Expected outcome
                5. Market context analysis

                Be specific and actionable.
                """

                # Get AI rationale
                ai_rationale = hybrid_brain.get_ai_analysis(rationale_prompt) if hasattr(hybrid_brain, 'get_ai_analysis') else "AI analysis not available"

                # Log forward test entry
                forward_test_entry = {
                    'symbol': symbol,
                    'prediction': prediction,
                    'ai_rationale': ai_rationale,
                    'market_context': market_context,
                    'entry_time': current_time.isoformat(),
                    'entry_price': market_context['current_price'],
                    'test_type': 'forward_test_24_5'
                }

                # Store in memory for later outcome analysis
                memory.store_memory(
                    content=f"Forward test prediction: {symbol} - {prediction}",
                    type_="FORWARD_TEST_PREDICTION",
                    meta=forward_test_entry
                )

                forward_test_results.append(forward_test_entry)

                print(f"[ForwardTest] 📊 {symbol}: Prediction={prediction}, Context={market_context['trend']}")

            except Exception as e:
                print(f"[ForwardTest] ❌ Error processing {symbol}: {e}")
                continue

        # Log cycle completion
        log_ai_decision(shared_state,
            f"24/5 Forward testing completed - {len(forward_test_results)} predictions logged with AI rationale",
            "forward_test")

        print(f"[ForwardTest] ✅ 24/5 Forward testing cycle completed - {len(forward_test_results)} predictions")
    except Exception as e:
        print(f"[ForwardTest] ❌ Error in forward testing: {e}")
        if 'shared_state' in locals():
            log_ai_decision(shared_state, f"Forward testing error: {str(e)}", "error")
    print("[TRACE] auto_forward_test_cycle BOTTOM")

def dedicated_forward_test_thread():
    """
    🚀 DEDICATED FORWARD TEST THREAD - BYPASSES SCHEDULER ENTIRELY
    This runs in its own thread and executes forward tests every 5 minutes
    """
    print("[DEDICATED_THREAD] 🚀 Starting dedicated forward testing thread...", flush=True)

    # Wait a bit for system to fully initialize
    time.sleep(30)

    while True:
        try:
            print("[DEDICATED_THREAD] ⏰ 5-minute interval reached - executing forward test", flush=True)
            auto_forward_test_cycle()

            # Wait for 5 minutes (300 seconds)
            print("[DEDICATED_THREAD] 😴 Sleeping for 5 minutes...", flush=True)
            time.sleep(300)

        except Exception as e:
            print(f"[DEDICATED_THREAD] ❌ Error in dedicated thread: {e}", flush=True)
            time.sleep(60)  # Wait 1 minute before retrying

def auto_backtest_cycle():
    """📌 Enhanced backtesting with AI evaluation - runs every 4-6 hours"""
    try:
        # Get shared state for logging decisions
        shared_state = get_shared_state()

        # Import required services
        from app.vectorbt_service import vectorbt_service
        from app.main import log_ai_decision
        from app.postgresql_memory import PostgresMemory
        from app.ai_brain import HybridBrain
        from datetime import datetime, timedelta
        import random
        import json

        print("[AIBacktest] 🧠 Starting AI-enhanced backtesting cycle...")

        # Initialize AI components
        memory = PostgresMemory()

        # Get or create HybridBrain with proper MLBrain dependency
        components = shared_state.get("components", {})
        hybrid_brain = components.get('hybrid_brain')
        if not hybrid_brain:
            from app.ai_brain import MLBrain
            ml_brain = components.get('ml_brain') or MLBrain()
            hybrid_brain = HybridBrain(ml_brain)

        # 📌 Use latest OHLCV data from MT5
        current_hour = datetime.now().hour
        is_comprehensive = (current_hour % 6 == 0)  # Comprehensive every 6 hours

        if is_comprehensive:
            print("[AutoBacktest] 🔍 DEEP HISTORICAL ANALYSIS MODE")
            log_ai_decision(shared_state, "Starting DEEP historical backtesting - Comprehensive strategy validation", "backtest")
        else:
            print("[AutoBacktest] ⚡ QUICK RECENT ANALYSIS MODE")
            log_ai_decision(shared_state, "Starting QUICK recent backtesting - Real-time strategy monitoring", "backtest")

        # Choose a symbol to backtest (could iterate all)
        for symbol in config.SYMBOLS:
            try:
                if is_comprehensive:
                    # 🔍 DEEP TEST: Historical date ranges (using actual historical dates)
                    from datetime import datetime, timedelta
                    now = datetime.now()

                    # Use recent historical periods that are more likely to have data
                    historical_periods = [
                        ((now - timedelta(days=30)).strftime('%Y-%m-%d'), (now - timedelta(days=15)).strftime('%Y-%m-%d')),  # 1 month ago to 2 weeks ago
                        ((now - timedelta(days=45)).strftime('%Y-%m-%d'), (now - timedelta(days=30)).strftime('%Y-%m-%d')),  # 1.5 months ago to 1 month ago
                        ((now - timedelta(days=60)).strftime('%Y-%m-%d'), (now - timedelta(days=45)).strftime('%Y-%m-%d')), # 2 months ago to 1.5 months ago
                        ((now - timedelta(days=90)).strftime('%Y-%m-%d'), (now - timedelta(days=75)).strftime('%Y-%m-%d')), # 3 months ago to 2.5 months ago
                    ]

                    # Randomly select a historical period for variety
                    start_date, end_date = random.choice(historical_periods)

                    print(f"[AutoBacktest] 🔍 DEEP: {symbol} historical analysis ({start_date} to {end_date})")

                    result = vectorbt_service.run_backtest_with_dates(
                        symbol=symbol,
                        strategy="ma_crossover",
                        initial_cash=10000,
                        start_date=start_date,
                        end_date=end_date
                    )

                    test_type = f"DEEP ({start_date} to {end_date})"

                else:
                    # ⚡ QUICK TEST: Recent candles
                    print(f"[AutoBacktest] ⚡ QUICK: {symbol} recent performance analysis")

                    result = vectorbt_service.run_backtest(
                        symbol=symbol,
                        strategy="ma_crossover",
                        initial_cash=10000,
                        num_candles=500
                    )

                    test_type = "QUICK (recent 500 candles)"

                # 📊 Process results
                if result and result.get('status') == 'completed':
                    trades_count = result.get('total_trades', 0)
                    return_pct = result.get('total_return_pct', 0)
                    win_rate = result.get('win_rate', 0)

                    log_ai_decision(shared_state,
                        f"{test_type} backtest completed for {symbol}: {trades_count} trades, {return_pct:.2f}% return, {win_rate:.1f}% win rate",
                        "backtest")

                    print(f"[AutoBacktest] ✅ {test_type} - {symbol}: {trades_count} trades, {return_pct:.2f}% return, {win_rate:.1f}% win rate")

                    # 🧠 AI EVALUATION with GPT/Claude scoring
                    max_drawdown = result.get('max_drawdown', 0)
                    sharpe_ratio = result.get('sharpe_ratio', 0)

                    ai_evaluation_prompt = f"""
                    Analyze this backtesting result as an expert quantitative analyst:

                    Strategy: ma_crossover
                    Symbol: {symbol}
                    Period: {test_type}
                    Total Return: {return_pct}%
                    Win Rate: {win_rate}%
                    Total Trades: {trades_count}
                    Max Drawdown: {max_drawdown}%
                    Sharpe Ratio: {sharpe_ratio}

                    Provide a score (0-100) and analysis:
                    1. Strategy effectiveness score
                    2. Risk management quality
                    3. Market adaptation ability
                    4. Overall performance score
                    5. Specific improvement suggestions

                    Format: Score: X/100, Analysis: [detailed analysis]
                    """

                    # Get AI evaluation
                    try:
                        if hasattr(hybrid_brain, 'get_ai_analysis'):
                            ai_evaluation = hybrid_brain.get_ai_analysis(ai_evaluation_prompt)
                        else:
                            ai_evaluation = f"Basic analysis: {return_pct:.2f}% return with {win_rate:.1f}% win rate"
                    except Exception as e:
                        ai_evaluation = f"AI evaluation failed: {str(e)}"

                    # Store enhanced result with AI evaluation
                    enhanced_result = {
                        'symbol': symbol,
                        'strategy': 'ma_crossover',
                        'test_type': test_type,
                        'trades_count': trades_count,
                        'return_pct': return_pct,
                        'win_rate': win_rate,
                        'max_drawdown': max_drawdown,
                        'sharpe_ratio': sharpe_ratio,
                        'ai_evaluation': ai_evaluation,
                        'timestamp': datetime.now().isoformat()
                    }

                    # Store in memory
                    memory.store_memory(
                        content=f"AI-evaluated backtest: {symbol} ma_crossover - {return_pct:.2f}% return",
                        type_="AI_BACKTEST_RESULT",
                        meta=enhanced_result
                    )

                    # 🧠 LEARNING: Store results for AI analysis
                    if trades_count > 10:  # Only learn from statistically significant results
                        learning_data = {
                            'symbol': symbol,
                            'test_type': 'comprehensive' if is_comprehensive else 'quick',
                            'return_pct': return_pct,
                            'win_rate': win_rate,
                            'trades': trades_count,
                            'ai_score': ai_evaluation[:100],  # First 100 chars of AI evaluation
                            'timestamp': datetime.now().isoformat()
                        }
                        print(f"[AIBacktest] 🧠 AI learning from {symbol} results: {trades_count} trades with AI evaluation")

                else:
                    log_ai_decision(shared_state, f"{test_type} backtest completed for {symbol} - No valid results", "backtest")

            except Exception as symbol_error:
                print(f"[AutoBacktest] ❌ Error backtesting {symbol}: {symbol_error}")
                log_ai_decision(shared_state, f"VectorBT backtest error for {symbol}: {str(symbol_error)}", "error")

    except Exception as e:
        print(f"[AutoBacktest] ❌ Error during hybrid backtest: {e}")
        if 'shared_state' in locals():
            log_ai_decision(shared_state, f"Automated backtest error: {str(e)}", "error")

def _analyze_trend(data: pd.DataFrame) -> str:
    """Helper function to analyze market trend"""
    try:
        if data.empty:
            return "unknown"

        close = data['close']
        ma_short = close.rolling(10).mean().iloc[-1]
        ma_long = close.rolling(50).mean().iloc[-1]
        current_price = close.iloc[-1]

        if current_price > ma_short > ma_long:
            return "strong_uptrend"
        elif current_price > ma_short and ma_short < ma_long:
            return "weak_uptrend"
        elif current_price < ma_short < ma_long:
            return "strong_downtrend"
        elif current_price < ma_short and ma_short > ma_long:
            return "weak_downtrend"
        else:
            return "sideways"
    except:
        return "unknown"

def _calculate_volatility(data: pd.DataFrame) -> str:
    """Helper function to calculate volatility regime"""
    try:
        if data.empty:
            return "unknown"

        high_low = data['high'] - data['low']
        volatility_percentile = high_low.rolling(100).rank(pct=True).iloc[-1]

        if volatility_percentile > 0.8:
            return "high_volatility"
        elif volatility_percentile < 0.2:
            return "low_volatility"
        else:
            return "normal_volatility"
    except:
        return "unknown"

def memory_rag_maintenance_cycle():
    """🧠 Memory-RAG system maintenance cycle"""
    try:
        print("[MemoryRAG] 🧠 Starting Memory-RAG maintenance cycle...")

        from app.memory_rag_system import memory_rag_system

        # Update memory index
        memory_rag_system.initialize_memory_index()

        # Get statistics
        stats = memory_rag_system.get_memory_statistics()

        print(f"[MemoryRAG] 📊 Memory stats: {stats.get('total_memories', 0)} memories, "
              f"Win rate: {stats.get('win_rate', 0):.1%}")

        # Log to shared state if available
        shared_state = get_shared_state()
        if shared_state:
            log_ai_decision(shared_state,
                f"Memory-RAG maintenance: {stats.get('total_memories', 0)} memories indexed",
                "memory_rag")

    except Exception as e:
        print(f"[MemoryRAG] ❌ Error in Memory-RAG maintenance: {e}")

def zone_detection_cycle():
    """🎯 Zone detection cycle for all symbols"""
    try:
        print("[ZoneDetection] 🎯 Starting zone detection cycle...")

        from app.zone_detection_system import zone_detection_system

        total_zones = 0

        for symbol in config.SYMBOLS:
            try:
                zones = zone_detection_system.detect_zones_for_symbol(symbol)
                total_zones += len(zones)
                print(f"[ZoneDetection] 📊 {symbol}: {len(zones)} zones detected")
            except Exception as e:
                print(f"[ZoneDetection] ❌ Error detecting zones for {symbol}: {e}")
                continue

        # Log to shared state if available
        shared_state = get_shared_state()
        if shared_state:
            log_ai_decision(shared_state,
                f"Zone detection: {total_zones} zones detected across {len(config.SYMBOLS)} symbols",
                "zone_detection")

        print(f"[ZoneDetection] ✅ Zone detection completed - {total_zones} total zones")

    except Exception as e:
        print(f"[ZoneDetection] ❌ Error in zone detection cycle: {e}")

def self_healing_backup_cycle():
    """🛡️ Self-healing automated backup cycle"""
    try:
        print("[SelfHealing] 🛡️ Starting automated backup cycle...")

        from app.self_healing_memory import self_healing_memory
        import asyncio

        # Create backup
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            backup_info = loop.run_until_complete(
                self_healing_memory.create_backup("scheduled")
            )

            if backup_info.status == backup_info.status.SUCCESS:
                print(f"[SelfHealing] ✅ Backup created: {backup_info.backup_id}")

                # Log to shared state if available
                shared_state = get_shared_state()
                if shared_state:
                    log_ai_decision(shared_state,
                        f"Self-healing backup created: {backup_info.backup_id} "
                        f"({backup_info.file_size / 1024 / 1024:.2f} MB)",
                        "self_healing")
            else:
                print(f"[SelfHealing] ❌ Backup failed: {backup_info.backup_id}")

        finally:
            loop.close()

    except Exception as e:
        print(f"[SelfHealing] ❌ Error in backup cycle: {e}")

def self_healing_health_check_cycle():
    """🔍 Self-healing health check and corruption detection cycle"""
    try:
        print("[SelfHealing] 🔍 Starting health check cycle...")

        from app.self_healing_memory import self_healing_memory
        import asyncio

        # Perform health check and corruption detection
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            healing_success = loop.run_until_complete(
                self_healing_memory.detect_and_heal_corruption()
            )

            if healing_success:
                print("[SelfHealing] ✅ System health check passed")
            else:
                print("[SelfHealing] ⚠️ System issues detected - check logs")

                # Log to shared state if available
                shared_state = get_shared_state()
                if shared_state:
                    log_ai_decision(shared_state,
                        "Self-healing system detected and attempted to fix issues",
                        "self_healing_alert")

        finally:
            loop.close()

    except Exception as e:
        print(f"[SelfHealing] ❌ Error in health check cycle: {e}")

def market_regime_cycle():
    """📊 Market regime detection cycle"""
    try:
        print("[MarketRegime] 📊 Starting market regime analysis cycle...")

        from app.market_regime_detector import market_regime_detector

        regime_results = []

        for symbol in config.SYMBOLS:
            try:
                regime = market_regime_detector.analyze_market_regime(symbol)
                regime_results.append({
                    'symbol': symbol,
                    'regime': regime.market_regime.value,
                    'confidence': regime.regime_confidence
                })
                print(f"[MarketRegime] 📊 {symbol}: {regime.market_regime.value} "
                      f"(confidence: {regime.regime_confidence:.1%})")
            except Exception as e:
                print(f"[MarketRegime] ❌ Error analyzing regime for {symbol}: {e}")
                continue

        # Get overall statistics
        stats = market_regime_detector.get_regime_statistics()

        # Log to shared state if available
        shared_state = get_shared_state()
        if shared_state:
            avg_confidence = stats.get('avg_regime_confidence', 0)
            log_ai_decision(shared_state,
                f"Market regime analysis: {len(regime_results)} symbols analyzed, "
                f"avg confidence: {avg_confidence:.1%}",
                "market_regime")

        print(f"[MarketRegime] ✅ Market regime analysis completed - {len(regime_results)} symbols")

    except Exception as e:
        print(f"[MarketRegime] ❌ Error in market regime cycle: {e}")

def black_swan_detection_cycle():
    """🦢 Black Swan detection and risk guard cycle"""
    try:
        print("[BlackSwan] 🦢 Starting Black Swan detection cycle...")

        # Import Black Swan detector
        from app.black_swan_detector import black_swan_detector

        # Run detection
        detected_events = black_swan_detector.detect_black_swan_events()

        if detected_events:
            print(f"[BlackSwan] 🚨 {len(detected_events)} Black Swan events detected!")

            # Log to shared state if available
            shared_state = get_shared_state()
            if shared_state:
                severity_levels = [event.severity.value for event in detected_events.values()]
                max_severity = max(severity_levels) if severity_levels else "normal"

                log_ai_decision(shared_state,
                    f"Black Swan detection: {len(detected_events)} events, max severity: {max_severity}",
                    "black_swan")

            # Check if any positions need emergency closure
            for symbol, event in detected_events.items():
                if event.risk_override:
                    print(f"[BlackSwan] 🚨 RISK OVERRIDE ACTIVE for {symbol} - {event.severity.value}")

                    # Close positions if extreme event
                    if event.severity.value in ['critical', 'extreme']:
                        try:
                            # This would integrate with your position management
                            print(f"[BlackSwan] 🔴 Emergency position closure recommended for {symbol}")
                        except Exception as e:
                            print(f"[BlackSwan] ❌ Error in emergency closure: {e}")
        else:
            print("[BlackSwan] ✅ No Black Swan events detected - Markets stable")

        # Check and update risk override status
        risk_status = black_swan_detector.get_risk_status()
        if risk_status.get('override_active'):
            print(f"[BlackSwan] 🛡️ Risk override active - {risk_status.get('active_events_count', 0)} events")

    except Exception as e:
        print(f"[BlackSwan] ❌ Error in Black Swan detection cycle: {e}")

def weekly_rl_training_cycle():
    """🧠 Weekly Reinforcement Learning training cycle"""
    try:
        print("[RLTraining] 🧠 Starting weekly RL training cycle...")

        # Import RL system
        from app.rl_training_system import rl_system

        if rl_system is None:
            print("[RLTraining] ⚠️ RL system not available - install stable-baselines3")
            return

        shared_state = get_shared_state()

        # Train RL agents for each major symbol
        training_results = []

        for symbol in config.SYMBOLS[:2]:  # Train on top 2 symbols to start
            try:
                print(f"[RLTraining] 🎯 Training RL agent for {symbol}...")

                # Train PPO agent (most stable for trading)
                result = rl_system.train_rl_agent(
                    algorithm='PPO',
                    symbol=symbol,
                    total_timesteps=50000,  # Start with moderate training
                    n_envs=2  # Parallel environments
                )

                training_results.append(result)

                # Test on historical data (use proper historical dates)
                from datetime import datetime, timedelta
                # Use historical dates - go back 60 days and test on 30-day period
                end_date = datetime.now() - timedelta(days=30)  # End 30 days ago
                start_date = end_date - timedelta(days=30)      # Start 60 days ago

                simulation_result = rl_system.simulate_on_vectorbt_data(
                    rl_system.current_models[f"PPO_{symbol}"]['model'],
                    symbol,
                    start_date.strftime("%Y-%m-%d"),
                    end_date.strftime("%Y-%m-%d")
                )

                print(f"[RLTraining] 📊 {symbol} RL simulation: {simulation_result.get('final_return', 0):.2%} return")

            except Exception as e:
                print(f"[RLTraining] ❌ Error training RL for {symbol}: {e}")
                continue

        # Log results
        if shared_state:
            log_ai_decision(shared_state,
                f"Weekly RL training completed - {len(training_results)} agents trained with episodic learning",
                "rl_training")

        print(f"[RLTraining] ✅ Weekly RL training completed - {len(training_results)} agents trained")

    except Exception as e:
        print(f"[RLTraining] ❌ Error in weekly RL training: {e}")

# Enhanced AI backtesting is now integrated into auto_backtest_cycle above

def weekly_comprehensive_backtest():
    """📊 Weekly comprehensive backtesting across multiple strategies and periods"""
    try:
        shared_state = get_shared_state()
        from app.vectorbt_service import vectorbt_service
        from app.main import log_ai_decision
        from datetime import datetime, timedelta

        print("[WeeklyBacktest] 📊 COMPREHENSIVE WEEKLY ANALYSIS STARTING...")
        log_ai_decision(shared_state, "Starting weekly comprehensive backtesting - Multi-strategy analysis across historical periods", "backtest")

        # 🎯 COMPREHENSIVE TEST PERIODS (using historical dates)
        from datetime import datetime, timedelta
        now = datetime.now()

        # Generate recent historical periods with data more likely to be available
        test_periods = []
        # Use shorter, more recent periods to ensure data availability
        for i in range(6, 1, -1):  # 6 months back to 1 month back (recent data)
            period_start = now - timedelta(days=30*i)
            period_end = now - timedelta(days=30*(i-1))
            period_name = period_start.strftime("%B %Y")

            test_periods.append((
                period_start.strftime('%Y-%m-%d'),
                period_end.strftime('%Y-%m-%d'),
                period_name
            ))

        # 🧠 MULTIPLE STRATEGIES
        strategies = ["ma_crossover", "rsi_oversold", "bollinger_bands"]

        comprehensive_results = {}

        for symbol in config.SYMBOLS:
            comprehensive_results[symbol] = {}

            for strategy in strategies:
                comprehensive_results[symbol][strategy] = []

                print(f"[WeeklyBacktest] 🔍 Testing {symbol} with {strategy} strategy...")

                for start_date, end_date, period_name in test_periods:
                    try:
                        result = vectorbt_service.run_backtest_with_dates(
                            symbol=symbol,
                            strategy=strategy,
                            initial_cash=10000,
                            start_date=start_date,
                            end_date=end_date
                        )

                        if result and result.get('status') == 'completed':
                            trades_count = result.get('total_trades', 0)
                            return_pct = result.get('total_return_pct', 0)
                            win_rate = result.get('win_rate', 0)

                            comprehensive_results[symbol][strategy].append({
                                'period': period_name,
                                'return_pct': return_pct,
                                'win_rate': win_rate,
                                'trades': trades_count
                            })

                            print(f"[WeeklyBacktest] ✅ {symbol} {strategy} {period_name}: {return_pct:.2f}% return, {trades_count} trades")

                    except Exception as period_error:
                        print(f"[WeeklyBacktest] ⚠️ Error testing {symbol} {strategy} {period_name}: {period_error}")

        # 📊 ANALYSIS & REPORTING
        print("[WeeklyBacktest] 📊 COMPREHENSIVE ANALYSIS COMPLETE")

        # Find best performing strategy per symbol
        for symbol in comprehensive_results:
            best_strategy = None
            best_avg_return = -999

            for strategy in comprehensive_results[symbol]:
                if comprehensive_results[symbol][strategy]:
                    avg_return = sum(r['return_pct'] for r in comprehensive_results[symbol][strategy]) / len(comprehensive_results[symbol][strategy])
                    total_trades = sum(r['trades'] for r in comprehensive_results[symbol][strategy])

                    if avg_return > best_avg_return and total_trades > 20:  # Minimum trade threshold
                        best_avg_return = avg_return
                        best_strategy = strategy

            if best_strategy:
                log_ai_decision(shared_state,
                    f"Weekly analysis: Best strategy for {symbol} is {best_strategy} with {best_avg_return:.2f}% average return",
                    "backtest")
                print(f"[WeeklyBacktest] 🏆 {symbol} best strategy: {best_strategy} ({best_avg_return:.2f}% avg return)")

        print("[WeeklyBacktest] ✅ Weekly comprehensive analysis completed")

    except Exception as e:
        print(f"[WeeklyBacktest] ❌ Error during weekly comprehensive backtest: {e}")
        if 'shared_state' in locals():
            log_ai_decision(shared_state, f"Weekly comprehensive backtest error: {str(e)}", "error")

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='NexGen Forex Trading Bot')
    parser.add_argument('--show-mt5', action='store_true', default=True,
                       help='Show MT5 terminal UI (default behavior)')
    parser.add_argument('--background-mt5', action='store_true', default=False,
                       help='Run MT5 terminals in background (hidden)')
    return parser.parse_args()

def configure_mt5_background_mode(show_ui=True):
    """Configure MT5 terminals to run normally with UI (default) or in background"""
    try:
        from app.settings_manager import SettingsManager
        settings_manager = SettingsManager()

        # Update MT5 terminal settings
        mt5_settings = {
            'background_mode': not show_ui,
            'hide_terminal_ui': not show_ui,
            'startup_delay': 5
        }

        settings_manager.update_setting('mt5_terminal', mt5_settings)

        if show_ui:
            print(f"🔧 MT5 terminals configured for UI mode")
        else:
            print(f"🔧 MT5 terminals configured for background mode")

    except Exception as e:
        print(f"⚠️ Error configuring MT5 mode: {e}")

if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()

    # Configure MT5 mode based on arguments
    show_mt5_ui = args.show_mt5 and not args.background_mt5
    configure_mt5_background_mode(show_ui=show_mt5_ui)

    if show_mt5_ui:
        print("🖥️ MT5 terminals will show UI")
    else:
        print("🔇 MT5 terminals will run in background (hidden mode)")

    # Continue with normal startup
    main()
